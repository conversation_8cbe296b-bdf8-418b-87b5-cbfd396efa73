<?php
/**
 * Plugin Name: Auto Image Generator Pro :: Enhanced & Merged v4.2
 * Description: Intelligently generates high-quality images for posts using AI (Gemini), optimizes them, and enhances the admin experience with a visually appealing and efficient interface. Uses the confirmed working API call structure.
 * Version: 4.2
 * Author: AI Enhanced Developer (Merged & Optimized)
 * License: GPL2
 * Requires PHP: 7.4
 * Requires at least: 5.8
 * Text Domain: auto-image-generator-pro
 * Domain Path: /languages
 */

declare(strict_types=1);

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Direct API implementation - no Python bridge required

// === Constants ===
define('AIG_PRO_VERSION', '5.0'); // Updated version with semantic search
define('AIG_PRO_OPTION_GROUP', 'aig_options');
define('AIG_PRO_SETTINGS_SLUG', 'auto-image-generator');
define('AIG_PRO_CACHE_PREFIX', 'aig_cache_');
define('AIG_PRO_GEMINI_CACHE_PREFIX', 'aig_gemini_v5_'); // Updated prefix for Gemini API call results to avoid potential conflicts
define('AIG_PRO_POST_CACHE_PREFIX', 'aig_post_v5_'); // Updated prefix for post-specific cache
define('AIG_PRO_NOTIFICATION_META', '_aig_notifications'); // Prefix with _ for standard hidden meta
define('AIG_PRO_MAX_IMAGES_LIMIT', 10); // Hard limit for safety/performance
define('AIG_PRO_API_TIMEOUT', 45); // Seconds for API calls
define('AIG_PRO_API_RETRY_DELAY', 3); // Seconds to wait before API retry
define('AIG_PRO_ASSETS_VERSION', AIG_PRO_VERSION . '.' . date('YmdHis', filemtime(__FILE__))); // Cache busting for embedded assets
define('AIG_PRO_DB_VERSION', '1.0'); // Database version for table creation/updates
define('AIG_PRO_IMAGE_VECTORS_TABLE', 'aig_image_vectors'); // Table name for image vectors

final class AIG_Pro {
    private static ?AIG_Pro $instance = null;

    private string $api_key = '';
    private string $image_style = 'realistic photo';
    private int $max_images = 3;
    private bool $image_optimization_enabled = true;
    private bool $use_webp = false;
    private bool $generate_all_sizes = true;
    private string $featured_text_style = 'centered';
    private string $current_keyword = ''; // Used for filename/alt text context
    private bool $use_semantic_search = true; // Enable semantic search by default
    private int $vector_dimension = 768; // Default dimension for embeddings
    private float $image_match_threshold = 0.65; // Minimum cosine similarity threshold
    private bool $enable_image_analysis = false; // Enable AI-powered image analysis
    private bool $force_alt_text_regeneration = false; // Force regeneration of alt text

    // Redis support removed to simplify the plugin

    // Background Processing & Rate Limiting
    private bool $background_tasks_enabled = true;
    private int $background_task_threshold = 3; // Generate > 3 images in background
    private int $api_request_count = 0;
    private int $api_request_limit = 50; // Gemini free tier limit is often 60/min, keep slightly lower
    private float $api_last_request_time = 0.0; // Use float for microtime
    private float $api_cooldown = 0.8; // Seconds between API requests

    public static function get_instance(): AIG_Pro {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    private function __construct() {
        $this->load_options();
        $this->add_hooks();
    }

    /**
     * Load plugin options from the database.
     * Uses strict typing and appropriate defaults.
     */
    private function load_options(): void {
        $options = get_option(AIG_PRO_OPTION_GROUP, []); // Get all options at once

        $this->api_key = (string) ($options['aig_api_key'] ?? get_option('aig_api_key', '')); // Keep fallback for old key location initially
        $this->image_style = (string) ($options['aig_image_style'] ?? get_option('aig_image_style', 'realistic photo'));
        $this->max_images = min(AIG_PRO_MAX_IMAGES_LIMIT, max(1, (int) ($options['aig_max_images'] ?? get_option('aig_max_images', 3))));
        $this->image_optimization_enabled = (bool) ($options['aig_optimize_images'] ?? get_option('aig_optimize_images', true));
        $this->use_webp = (bool) ($options['aig_use_webp'] ?? get_option('aig_use_webp', false));
        $this->generate_all_sizes = (bool) ($options['aig_generate_all_sizes'] ?? get_option('aig_generate_all_sizes', true));
        $this->featured_text_style = (string) ($options['aig_featured_text_style'] ?? get_option('aig_featured_text_style', 'centered'));

        // Load semantic search and AI features
        $this->use_semantic_search = (bool) ($options['aig_use_semantic_search'] ?? get_option('aig_use_semantic_search', true));
        $this->image_match_threshold = (float) ($options['aig_image_match_threshold'] ?? get_option('aig_image_match_threshold', 0.65));
        $this->enable_image_analysis = (bool) ($options['aig_enable_image_analysis'] ?? get_option('aig_enable_image_analysis', false));
        $this->force_alt_text_regeneration = (bool) ($options['aig_force_alt_text_regeneration'] ?? get_option('aig_force_alt_text_regeneration', false));

        // Redis support removed to simplify the plugin
    }

    // Redis support removed to simplify the plugin

    private function add_hooks(): void {
        // Core Plugin Hooks
        add_action('admin_menu', [$this, 'add_menu_pages']);
        add_action('admin_init', [$this, 'register_settings']);
        add_action('admin_head', [$this, 'embed_admin_scripts_and_styles']); // Embed assets

        // AJAX Handlers
        add_action('wp_ajax_aig_generate_images', [$this, 'ajax_generate_images']);
        add_action('wp_ajax_aig_clear_cache', [$this, 'ajax_clear_cache']);
        add_action('wp_ajax_aig_optimize_alt_text', [$this, 'ajax_optimize_alt_text']);
        add_action('wp_ajax_aig_bulk_optimize_alt_text', [$this, 'ajax_bulk_optimize_alt_text']);
        add_action('wp_ajax_aig_rebuild_index', [$this, 'ajax_rebuild_index']);

        // Background Task (WP Cron)
        add_action('aig_background_generate_images', [$this, 'background_generate_images'], 10, 4);

        // Action Scheduler Integration
        add_action('aig_process_post_action_scheduler', [$this, 'process_post_action_scheduler'], 10, 4);

        // Embedding and Alt Text Processing
        add_action('aig_rebuild_embedding_batch', [$this, 'process_embedding_batch'], 10, 1);
        add_action('aig_rebuild_embedding_single', [$this, 'process_embedding_single'], 10, 1);
        add_action('aig_optimize_alt_text_batch', [$this, 'process_alt_text_batch'], 10, 1);
        add_action('aig_optimize_alt_text_single', [$this, 'process_alt_text_single'], 10, 1);

        // Database Setup
        register_activation_hook(__FILE__, [$this, 'activate_plugin']);

        // Image Processing Filters
        add_filter('intermediate_image_sizes_advanced', [$this, 'filter_intermediate_image_sizes'], 999);

        // Add filter for attachment metadata
        add_filter('wp_generate_attachment_metadata', [$this, 'process_attachment_metadata'], 10, 2);

        // Dashboard Widget
        add_action('wp_dashboard_setup', [$this, 'add_dashboard_widget']);

        // REST API Endpoint
        add_action('rest_api_init', [$this, 'register_rest_api']);

        // Post Edit Screen Meta Box
        add_action('add_meta_boxes', [$this, 'add_meta_box']);

        // Admin Notifications
        add_action('admin_notices', [$this, 'display_completion_notifications']);

        // Cache Clearing Hooks (Activation/Deactivation)
        register_deactivation_hook(__FILE__, [$this, 'clear_all_plugin_cache']);

        // Plugin Action Links
        add_filter('plugin_action_links_' . plugin_basename(__FILE__), [$this, 'add_action_links']);

        // Load Text Domain for Localization
        add_action('plugins_loaded', [$this, 'load_textdomain']);

        // Register Action Scheduler cleanup callback if available
        if (function_exists('as_register_cleanup_callback')) {
            as_register_cleanup_callback([$this, 'action_scheduler_cleanup']);
        }
    }

    /**
     * Cleanup callback for Action Scheduler.
     * This is called when an action is canceled or deleted.
     */
    public function action_scheduler_cleanup(): void {
        // Clean up any temporary files or resources
        error_log("[AIG Pro] Action Scheduler cleanup called");
    }

    /**
     * Plugin activation hook callback.
     * Creates or updates database tables and performs other activation tasks.
     */
    public function activate_plugin(): void {
        // Clear cache
        $this->clear_all_plugin_cache();

        // Create or update database tables
        $this->create_or_update_tables();
    }

    /**
     * Creates or updates the plugin's database tables.
     * Checks the current DB version and updates if needed.
     */
    private function create_or_update_tables(): void {
        global $wpdb;

        $current_db_version = get_option('aig_db_version', '0');

        // If already at the current version, no need to run the update
        if (version_compare($current_db_version, AIG_PRO_DB_VERSION, '>=')) {
            return;
        }

        // Include WordPress database upgrade functions
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');

        // Image Vectors Table
        $table_name = $wpdb->prefix . AIG_PRO_IMAGE_VECTORS_TABLE;
        $charset_collate = $wpdb->get_charset_collate();

        $sql = "CREATE TABLE $table_name (
            id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
            attachment_id bigint(20) unsigned NOT NULL,
            vector_data longtext NOT NULL,
            vector_type varchar(50) NOT NULL DEFAULT 'gemini',
            vector_dimension int(11) NOT NULL DEFAULT 768,
            created_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY  (id),
            KEY attachment_id (attachment_id),
            KEY vector_type (vector_type)
        ) $charset_collate;";

        dbDelta($sql);

        // Update the database version option
        update_option('aig_db_version', AIG_PRO_DB_VERSION);

        error_log("[AIG Pro] Database tables created/updated to version " . AIG_PRO_DB_VERSION);
    }

    // --- Settings & Admin Page ---

    public function add_menu_pages(): void {
        add_menu_page(
            __('Auto Image Generator Pro', 'auto-image-generator-pro'),
            __('Auto Images', 'auto-image-generator-pro'),
            'manage_options',
            AIG_PRO_SETTINGS_SLUG,
            [$this, 'render_admin_page'],
            'dashicons-camera-alt', // Changed icon for better visual
            30
        );
    }

    /**
     * Register plugin settings, sections, and fields.
     * Uses single option array 'aig_options' for efficiency.
     */
    public function register_settings(): void {
        register_setting(AIG_PRO_OPTION_GROUP, AIG_PRO_OPTION_GROUP, [
            'type' => 'array',
            'sanitize_callback' => [$this, 'sanitize_options'],
            'default' => [ // Define defaults here
                'aig_api_key' => '',
                'aig_image_style' => 'realistic photo',
                'aig_max_images' => 3,
                'aig_optimize_images' => true,
                'aig_use_webp' => false,
                'aig_generate_all_sizes' => true,
                'aig_featured_text_style' => 'centered',
            ],
        ]);

        // --- Sections ---
        add_settings_section('aig_main_section', __('AI & Generation', 'auto-image-generator-pro'), fn() => print('<p>' . __('Configure your Gemini API key and default generation settings.', 'auto-image-generator-pro') . '</p>'), AIG_PRO_SETTINGS_SLUG);
        add_settings_section('aig_semantic_section', __('Semantic Search & AI Features', 'auto-image-generator-pro'), fn() => print('<p>' . __('Configure advanced AI features for image search and metadata enhancement.', 'auto-image-generator-pro') . '</p>'), AIG_PRO_SETTINGS_SLUG);
        add_settings_section('aig_optimization_section', __('Optimization & Performance', 'auto-image-generator-pro'), fn() => print('<p>' . __('Control image optimization, WebP generation, and image sizes.', 'auto-image-generator-pro') . '</p>'), AIG_PRO_SETTINGS_SLUG);
        add_settings_section('aig_cache_section', __('Caching', 'auto-image-generator-pro'), fn() => print('<p>' . __('Configure caching for improved performance.', 'auto-image-generator-pro') . '</p>'), AIG_PRO_SETTINGS_SLUG);
        add_settings_section('aig_advanced_section', __('Advanced & Maintenance', 'auto-image-generator-pro'), fn() => print('<p>' . __('Tools for managing plugin data and operations.', 'auto-image-generator-pro') . '</p>'), AIG_PRO_SETTINGS_SLUG);

        // --- Fields ---
        add_settings_field('aig_api_key', __('Gemini API Key', 'auto-image-generator-pro'), [$this, 'render_api_key_field'], AIG_PRO_SETTINGS_SLUG, 'aig_main_section');
        add_settings_field('aig_image_style', __('Default Image Style', 'auto-image-generator-pro'), [$this, 'render_image_style_field'], AIG_PRO_SETTINGS_SLUG, 'aig_main_section');
        add_settings_field('aig_featured_text_style', __('Featured Image Prompt Hint', 'auto-image-generator-pro'), [$this, 'render_featured_text_style_field'], AIG_PRO_SETTINGS_SLUG, 'aig_main_section');
        add_settings_field('aig_max_images', __('Max Images Per Post', 'auto-image-generator-pro'), [$this, 'render_max_images_field'], AIG_PRO_SETTINGS_SLUG, 'aig_main_section');

        // Semantic Search Fields
        add_settings_field('aig_use_semantic_search', __('Use Semantic Search', 'auto-image-generator-pro'), [$this, 'render_use_semantic_search_field'], AIG_PRO_SETTINGS_SLUG, 'aig_semantic_section');
        add_settings_field('aig_image_match_threshold', __('Semantic Match Threshold', 'auto-image-generator-pro'), [$this, 'render_image_match_threshold_field'], AIG_PRO_SETTINGS_SLUG, 'aig_semantic_section');
        add_settings_field('aig_alt_text_generation', __('AI Alt Text Generation', 'auto-image-generator-pro'), [$this, 'render_alt_text_generation_field'], AIG_PRO_SETTINGS_SLUG, 'aig_semantic_section');

        add_settings_field('aig_optimize_images', __('Optimize Generated Images', 'auto-image-generator-pro'), [$this, 'render_optimize_images_field'], AIG_PRO_SETTINGS_SLUG, 'aig_optimization_section');
        add_settings_field('aig_use_webp', __('Generate WebP Versions', 'auto-image-generator-pro'), [$this, 'render_use_webp_field'], AIG_PRO_SETTINGS_SLUG, 'aig_optimization_section');
        add_settings_field('aig_generate_all_sizes', __('Generate Standard WP Sizes', 'auto-image-generator-pro'), [$this, 'render_generate_all_sizes_field'], AIG_PRO_SETTINGS_SLUG, 'aig_optimization_section');

        // Cache Settings - Redis support removed

        add_settings_field('aig_clear_cache', __('Cache Control', 'auto-image-generator-pro'), [$this, 'render_clear_cache_field'], AIG_PRO_SETTINGS_SLUG, 'aig_advanced_section');
    }

    /**
     * Sanitize the entire options array before saving.
     */
    public function sanitize_options(array $input): array {
        $new_input = [];
        $defaults = (array) get_option(AIG_PRO_OPTION_GROUP, []); // Use current values or defaults

        $new_input['aig_api_key'] = isset($input['aig_api_key']) ? sanitize_text_field($input['aig_api_key']) : ($defaults['aig_api_key'] ?? '');
        $new_input['aig_image_style'] = isset($input['aig_image_style']) ? sanitize_text_field($input['aig_image_style']) : ($defaults['aig_image_style'] ?? 'realistic photo');
        $new_input['aig_max_images'] = isset($input['aig_max_images']) ? min(AIG_PRO_MAX_IMAGES_LIMIT, max(1, intval($input['aig_max_images']))) : ($defaults['aig_max_images'] ?? 3);
        $new_input['aig_optimize_images'] = isset($input['aig_optimize_images']) ? (bool)$input['aig_optimize_images'] : ($defaults['aig_optimize_images'] ?? true);
        $new_input['aig_use_webp'] = isset($input['aig_use_webp']) ? (bool)$input['aig_use_webp'] : ($defaults['aig_use_webp'] ?? false);
        $new_input['aig_generate_all_sizes'] = isset($input['aig_generate_all_sizes']) ? (bool)$input['aig_generate_all_sizes'] : ($defaults['aig_generate_all_sizes'] ?? true);
        $new_input['aig_featured_text_style'] = isset($input['aig_featured_text_style']) ? sanitize_text_field($input['aig_featured_text_style']) : ($defaults['aig_featured_text_style'] ?? 'centered');

        // Semantic search and AI features
        $new_input['aig_use_semantic_search'] = isset($input['aig_use_semantic_search']) ? (bool)$input['aig_use_semantic_search'] : ($defaults['aig_use_semantic_search'] ?? true);
        $new_input['aig_image_match_threshold'] = isset($input['aig_image_match_threshold']) ?
            min(1.0, max(0.1, floatval($input['aig_image_match_threshold']))) :
            ($defaults['aig_image_match_threshold'] ?? 0.65);
        $new_input['aig_alt_text_generation'] = isset($input['aig_alt_text_generation']) ? (bool)$input['aig_alt_text_generation'] : ($defaults['aig_alt_text_generation'] ?? true);
        $new_input['aig_enable_image_analysis'] = isset($input['aig_enable_image_analysis']) ? (bool)$input['aig_enable_image_analysis'] : ($defaults['aig_enable_image_analysis'] ?? false);
        $new_input['aig_force_alt_text_regeneration'] = isset($input['aig_force_alt_text_regeneration']) ? (bool)$input['aig_force_alt_text_regeneration'] : ($defaults['aig_force_alt_text_regeneration'] ?? false);

        // Redis settings removed to simplify the plugin

        // Prevent saving WebP if GD support is missing
        if ($new_input['aig_use_webp'] && !function_exists('imagewebp')) {
             $new_input['aig_use_webp'] = false;
             add_settings_error('aig_use_webp', 'webp_not_supported', __('WebP generation disabled because the GD library with WebP support is not available on your server.', 'auto-image-generator-pro'), 'warning');
        }

        return $new_input;
    }


    // --- Embedding and Semantic Search Functions ---

    /**
     * Calculate cosine similarity between two vectors.
     *
     * @param array $vec1 First vector
     * @param array $vec2 Second vector
     * @return float Cosine similarity value between -1 and 1
     */
    public function cosine_similarity(array $vec1, array $vec2): float {
        $dotProduct = 0.0;
        $normVec1 = 0.0;
        $normVec2 = 0.0;
        $count = count($vec1);

        if ($count === 0 || $count !== count($vec2)) {
            return 0.0;
        }

        for ($i = 0; $i < $count; $i++) {
            $dotProduct += $vec1[$i] * $vec2[$i];
            $normVec1 += $vec1[$i] * $vec1[$i];
            $normVec2 += $vec2[$i] * $vec2[$i];
        }

        if ($normVec1 == 0 || $normVec2 == 0) {
            return 0.0;
        }

        return $dotProduct / (sqrt($normVec1) * sqrt($normVec2));
    }

    /**
     * Generate embedding for text content using Gemini API.
     *
     * @param string $text Text to generate embedding for
     * @return array|null Array of embedding values or null on failure
     */
    public function generate_embedding(string $text): ?array {
        if (empty($this->api_key) || empty($text)) {
            return null;
        }

        $text = trim($text);
        if (empty($text)) {
            return null;
        }

        // Cache key for this specific text
        $cache_key = AIG_PRO_GEMINI_CACHE_PREFIX . 'embed_' . md5($text);

        // Check transient cache
        $cached_data = get_transient($cache_key);
        if ($cached_data && is_array($cached_data)) {
            return $cached_data;
        }

        // Prepare API request
        $api_url = 'https://generativelanguage.googleapis.com/v1/models/embedding-001:embedContent?key=' . $this->api_key;

        $request_data = [
            'model' => 'embedding-001',
            'content' => [
                'parts' => [
                    ['text' => $text]
                ]
            ]
        ];

        $args = [
            'headers' => [
                'Content-Type' => 'application/json',
            ],
            'body' => json_encode($request_data),
            'timeout' => AIG_PRO_API_TIMEOUT,
        ];

        // Make API request
        $response = wp_remote_post($api_url, $args);

        if (is_wp_error($response)) {
            error_log("[AIG Pro] Embedding API Error: " . $response->get_error_message());
            return null;
        }

        $response_code = wp_remote_retrieve_response_code($response);
        if ($response_code !== 200) {
            error_log("[AIG Pro] Embedding API Error: HTTP $response_code - " . wp_remote_retrieve_body($response));
            return null;
        }

        $body = json_decode(wp_remote_retrieve_body($response), true);

        if (!isset($body['embedding']['values']) || !is_array($body['embedding']['values'])) {
            error_log("[AIG Pro] Embedding API Error: Invalid response format");
            return null;
        }

        $embedding = $body['embedding']['values'];

        // Cache the result
        set_transient($cache_key, $embedding, 7 * 86400); // 7 days in seconds

        return $embedding;
    }

    /**
     * Generate a detailed scene description using Gemini text model.
     * This creates a rich, contextual prompt for image generation based on the content.
     *
     * @param string $keyword The primary keyword for the image
     * @param string $content_snippet The surrounding content for context
     * @param string $post_title The title of the post
     * @return string|null The generated scene description or null on failure
     */
    private function generate_scene_description_with_gemini(string $keyword, string $content_snippet, string $post_title): ?string {
        if (empty($this->api_key)) {
            return null;
        }

        // Use a faster model for scene description generation
        $text_model = apply_filters('aig_gemini_text_model_for_scene', 'gemini-1.5-flash-latest');
        $text_api_url = "https://generativelanguage.googleapis.com/v1beta/models/{$text_model}:generateContent?key=" . urlencode($this->api_key);

        // Create a prompt instruction for the text model
        $prompt_instruction = sprintf(
            'Based on the keyword "%s", post title "%s", and the following content snippet: "%s". Generate a concise, vivid, and highly descriptive scene description suitable for an AI image generator. The scene should be directly relevant to the keyword and context. Do not include any meta-commentary, just the scene description. Max 70 words. Emphasize key visual elements, mood, and action if applicable. Avoid lists, output a single descriptive paragraph.',
            addslashes($keyword),
            addslashes($post_title),
            addslashes(wp_trim_words($content_snippet, 200, '...'))
        );

        // Create a cache key for the scene description
        $scene_cache_key = AIG_PRO_CACHE_PREFIX . 'scene_desc_' . md5($keyword . $post_title . substr($content_snippet, 0, 100));

        // Check cache first
        $cached_scene = $this->get_cache($scene_cache_key);
        if ($cached_scene) {
            return $cached_scene;
        }

        // Prepare the request data
        $request_data = [
            'contents' => [['role' => 'user', 'parts' => [['text' => $prompt_instruction]]]],
            'generationConfig' => [
                'temperature' => 0.6, // More creative for description
                'topP' => 0.9,
                'maxOutputTokens' => 150, // Enough for a ~70 word description
            ],
            'safetySettings' => [
                ['category' => 'HARM_CATEGORY_HARASSMENT', 'threshold' => 'BLOCK_MEDIUM_AND_ABOVE'],
                ['category' => 'HARM_CATEGORY_HATE_SPEECH', 'threshold' => 'BLOCK_MEDIUM_AND_ABOVE'],
                ['category' => 'HARM_CATEGORY_SEXUALLY_EXPLICIT', 'threshold' => 'BLOCK_MEDIUM_AND_ABOVE'],
                ['category' => 'HARM_CATEGORY_DANGEROUS_CONTENT', 'threshold' => 'BLOCK_MEDIUM_AND_ABOVE'],
            ],
        ];

        // Make the API request
        $args = [
            'headers' => [
                'Content-Type' => 'application/json',
            ],
            'body' => json_encode($request_data),
            'timeout' => AIG_PRO_API_TIMEOUT,
        ];

        $response = wp_remote_post($text_api_url, $args);

        // Handle errors
        if (is_wp_error($response)) {
            error_log("[AIG Pro] Scene Description API Error: " . $response->get_error_message());
            return null;
        }

        $response_code = wp_remote_retrieve_response_code($response);
        if ($response_code !== 200) {
            error_log("[AIG Pro] Scene Description API Error: HTTP $response_code - " . wp_remote_retrieve_body($response));
            return null;
        }

        // Parse the response
        $body = wp_remote_retrieve_body($response);
        $data = json_decode($body, true);

        if (isset($data['candidates'][0]['content']['parts'][0]['text'])) {
            $scene_description = trim($data['candidates'][0]['content']['parts'][0]['text']);

            // Clean up the description (remove common AI prefixes)
            $scene_description = preg_replace('/^(scene description|description|scene):?\s*/i', '', $scene_description);

            // Cache the result
            $this->set_cache($scene_cache_key, $scene_description, HOUR_IN_SECONDS * 12);

            return $scene_description;
        }

        error_log("[AIG Pro] Failed to generate scene description from text LLM. Keyword: $keyword");
        return null;
    }

    /**
     * Helper method to get cached data from transients
     *
     * @param string $key The cache key
     * @return mixed The cached data or false if not found
     */
    private function get_cache(string $key) {
        // Redis support removed to simplify the plugin
        return get_transient(str_replace(AIG_PRO_CACHE_PREFIX, '', $key));
    }

    /**
     * Helper method to set cached data in transients
     *
     * @param string $key The cache key
     * @param mixed $value The data to cache
     * @param int $expiration The expiration time in seconds
     */
    private function set_cache(string $key, $value, int $expiration): void {
        // Redis support removed to simplify the plugin
        set_transient(str_replace(AIG_PRO_CACHE_PREFIX, '', $key), $value, $expiration);
    }

    /**
     * Helper method to delete cached data from transients
     *
     * @param string $key The cache key
     */
    private function delete_cache(string $key): void {
        // Redis support removed to simplify the plugin
        delete_transient(str_replace(AIG_PRO_CACHE_PREFIX, '', $key));
    }

    /**
     * Store embedding for an attachment in the database.
     *
     * @param int $attachment_id Attachment ID
     * @param array $embedding Embedding vector
     * @param string $vector_type Type of vector (default: 'gemini')
     * @return bool Success or failure
     */
    public function store_image_embedding(int $attachment_id, array $embedding, string $vector_type = 'gemini'): bool {
        global $wpdb;

        if ($attachment_id <= 0 || empty($embedding)) {
            return false;
        }

        $table_name = $wpdb->prefix . AIG_PRO_IMAGE_VECTORS_TABLE;

        // Check if embedding already exists
        $existing = $wpdb->get_var($wpdb->prepare(
            "SELECT id FROM $table_name WHERE attachment_id = %d AND vector_type = %s",
            $attachment_id,
            $vector_type
        ));

        $vector_data = json_encode($embedding);
        $vector_dimension = count($embedding);

        if ($existing) {
            // Update existing record
            $result = $wpdb->update(
                $table_name,
                [
                    'vector_data' => $vector_data,
                    'vector_dimension' => $vector_dimension,
                    'updated_at' => current_time('mysql')
                ],
                [
                    'attachment_id' => $attachment_id,
                    'vector_type' => $vector_type
                ]
            );
        } else {
            // Insert new record
            $result = $wpdb->insert(
                $table_name,
                [
                    'attachment_id' => $attachment_id,
                    'vector_data' => $vector_data,
                    'vector_type' => $vector_type,
                    'vector_dimension' => $vector_dimension,
                    'created_at' => current_time('mysql'),
                    'updated_at' => current_time('mysql')
                ]
            );
        }

        return $result !== false;
    }

    /**
     * Get embedding for an attachment from the database.
     *
     * @param int $attachment_id Attachment ID
     * @param string $vector_type Type of vector (default: 'gemini')
     * @return array|null Embedding vector or null if not found
     */
    public function get_image_embedding(int $attachment_id, string $vector_type = 'gemini'): ?array {
        global $wpdb;

        if ($attachment_id <= 0) {
            return null;
        }

        $table_name = $wpdb->prefix . AIG_PRO_IMAGE_VECTORS_TABLE;

        $vector_data = $wpdb->get_var($wpdb->prepare(
            "SELECT vector_data FROM $table_name WHERE attachment_id = %d AND vector_type = %s",
            $attachment_id,
            $vector_type
        ));

        if (!$vector_data) {
            return null;
        }

        $embedding = json_decode($vector_data, true);

        if (!is_array($embedding)) {
            return null;
        }

        return $embedding;
    }

    /**
     * Find the most similar images to a given text using embeddings.
     *
     * @param string $text Text to compare images against
     * @param int $limit Maximum number of images to return
     * @param float $threshold Minimum similarity threshold (0-1)
     * @return array Array of image IDs with similarity scores
     */
    public function find_similar_images(string $text, int $limit = 5, float $threshold = 0.65): array {
        global $wpdb;

        if (empty($text) || $limit <= 0) {
            return [];
        }

        // Generate embedding for the text
        $text_embedding = $this->generate_embedding($text);

        if (!$text_embedding) {
            return [];
        }

        $table_name = $wpdb->prefix . AIG_PRO_IMAGE_VECTORS_TABLE;

        // Get all image embeddings
        $results = $wpdb->get_results(
            $wpdb->prepare(
                "SELECT attachment_id, vector_data FROM $table_name WHERE vector_type = %s",
                'gemini'
            )
        );

        if (!$results) {
            return [];
        }

        $similarities = [];

        // Calculate similarity for each image
        foreach ($results as $result) {
            $image_embedding = json_decode($result->vector_data, true);

            if (!is_array($image_embedding)) {
                continue;
            }

            $similarity = $this->cosine_similarity($text_embedding, $image_embedding);

            if ($similarity >= $threshold) {
                $similarities[$result->attachment_id] = $similarity;
            }
        }

        // Sort by similarity (highest first)
        arsort($similarities);

        // Limit results
        return array_slice($similarities, 0, $limit, true);
    }

    /**
     * Process attachment metadata to generate and store embeddings.
     * Also generates AI-powered alt text and captions if enabled.
     *
     * @param array $metadata Attachment metadata
     * @param int $attachment_id Attachment ID
     * @return array Unmodified metadata (for filter)
     */
    public function process_attachment_metadata($metadata, $attachment_id): array {
        // Skip if not an image
        if (!wp_attachment_is_image($attachment_id)) {
            return $metadata;
        }

        // Get attachment details
        $attachment = get_post($attachment_id);
        if (!$attachment) {
            return $metadata;
        }

        // Prepare text for embedding
        $text_parts = [];

        // Title
        if (!empty($attachment->post_title)) {
            $text_parts[] = $attachment->post_title;
        }

        // Caption
        if (!empty($attachment->post_excerpt)) {
            $text_parts[] = $attachment->post_excerpt;
        }

        // Alt text
        $alt_text = get_post_meta($attachment_id, '_wp_attachment_image_alt', true);
        if (!empty($alt_text)) {
            $text_parts[] = $alt_text;
        }

        // Description
        if (!empty($attachment->post_content)) {
            $text_parts[] = $attachment->post_content;
        }

        // Filename (without extension)
        $filename = pathinfo(get_attached_file($attachment_id), PATHINFO_FILENAME);
        $filename = str_replace(['-', '_'], ' ', $filename);
        if (!empty($filename)) {
            $text_parts[] = $filename;
        }

        // Combine all text
        $text = implode(' ', $text_parts);

        if (empty($text)) {
            // If no text is available, use a generic description based on mime type
            $mime_type = get_post_mime_type($attachment_id);
            $text = "Image of type $mime_type";
        }

        // Generate and store embedding
        $embedding = $this->generate_embedding($text);
        if ($embedding) {
            $this->store_image_embedding($attachment_id, $embedding);
        }

        // Generate alt text and caption if enabled
        $options = get_option(AIG_PRO_OPTION_GROUP, []);
        $alt_text_generation_enabled = isset($options['aig_alt_text_generation']) ? (bool)$options['aig_alt_text_generation'] : true;

        if ($alt_text_generation_enabled) {
            $this->generate_alt_text_and_caption($attachment_id);
        }

        return $metadata;
    }

    /**
     * Generate AI-powered alt text and caption for an image.
     *
     * @param int $attachment_id Attachment ID
     * @return bool Success or failure
     */
    public function generate_alt_text_and_caption(int $attachment_id): bool {
        // Skip if not an image
        if (!wp_attachment_is_image($attachment_id)) {
            return false;
        }

        // Get attachment details
        $attachment = get_post($attachment_id);
        if (!$attachment) {
            return false;
        }

        // Check if alt text needs improvement
        $alt_text = get_post_meta($attachment_id, '_wp_attachment_image_alt', true);
        $alt_text_needs_improvement = empty($alt_text) || strlen($alt_text) < 20;

        // Check if caption needs improvement
        $caption = $attachment->post_excerpt;
        $caption_needs_improvement = empty($caption) || strlen($caption) < 30;

        // If both are good, skip
        if (!$alt_text_needs_improvement && !$caption_needs_improvement) {
            return true;
        }

        // Get image URL for context
        $image_url = wp_get_attachment_url($attachment_id);
        if (!$image_url) {
            return false;
        }

        // Get image metadata
        $image_meta = wp_get_attachment_metadata($attachment_id);
        $width = $image_meta['width'] ?? 0;
        $height = $image_meta['height'] ?? 0;

        // Get image title and filename for context
        $title = $attachment->post_title;
        $filename = pathinfo(get_attached_file($attachment_id), PATHINFO_FILENAME);
        $filename = str_replace(['-', '_'], ' ', $filename);

        // Prepare prompt for Gemini
        $prompt = "I need to generate ";
        if ($alt_text_needs_improvement && $caption_needs_improvement) {
            $prompt .= "both alt text and a caption";
        } elseif ($alt_text_needs_improvement) {
            $prompt .= "alt text";
        } else {
            $prompt .= "a caption";
        }
        $prompt .= " for an image.\n\n";

        $prompt .= "Image Information:\n";
        $prompt .= "- Title: " . ($title ?: 'Not provided') . "\n";
        $prompt .= "- Filename: " . ($filename ?: 'Not provided') . "\n";
        if (!empty($alt_text)) {
            $prompt .= "- Current Alt Text: $alt_text\n";
        }
        if (!empty($caption)) {
            $prompt .= "- Current Caption: $caption\n";
        }
        if ($width && $height) {
            $prompt .= "- Dimensions: {$width}x{$height} pixels\n";
        }

        $prompt .= "\nRequirements:\n";

        if ($alt_text_needs_improvement) {
            $prompt .= "- Alt Text: Create a concise, descriptive alt text (10-20 words) that accurately describes the image content. Do not use phrases like 'image of' or 'picture of'. Focus on what's visually present and important. Make it useful for visually impaired users and SEO.\n";
        }

        if ($caption_needs_improvement) {
            $prompt .= "- Caption: Create an engaging, informative caption (1-2 sentences) that provides context or additional information about the image. The caption should complement the image and enhance understanding.\n";
        }

        $prompt .= "\nOutput Format:\n";
        $prompt .= "Return a JSON object with the following structure:\n";
        $prompt .= "{\n";
        if ($alt_text_needs_improvement) {
            $prompt .= "  \"alt_text\": \"The generated alt text\",\n";
        }
        if ($caption_needs_improvement) {
            $prompt .= "  \"caption\": \"The generated caption\"\n";
        }
        $prompt .= "}\n";
        $prompt .= "Only return the JSON object, nothing else.";

        // Call Gemini API for text generation (not image generation)
        $response = $this->call_gemini_api_text($prompt);

        if (empty($response)) {
            error_log("[AIG Pro] Failed to generate alt text/caption for attachment ID: $attachment_id");
            return false;
        }

        // Extract JSON from response
        $json_start = strpos($response, '{');
        $json_end = strrpos($response, '}');

        if ($json_start === false || $json_end === false) {
            error_log("[AIG Pro] Invalid JSON response for alt text/caption generation: $response");
            return false;
        }

        $json_string = substr($response, $json_start, $json_end - $json_start + 1);
        $data = json_decode($json_string, true);

        if (!$data) {
            error_log("[AIG Pro] Failed to parse JSON response for alt text/caption generation: $json_string");
            return false;
        }

        // Update alt text if needed and provided
        if ($alt_text_needs_improvement && isset($data['alt_text']) && !empty($data['alt_text'])) {
            update_post_meta($attachment_id, '_wp_attachment_image_alt', $data['alt_text']);
        }

        // Update caption if needed and provided
        if ($caption_needs_improvement && isset($data['caption']) && !empty($data['caption'])) {
            wp_update_post([
                'ID' => $attachment_id,
                'post_excerpt' => $data['caption']
            ]);
        }

        return true;
    }

    /**
     * AJAX handler for optimizing alt text and captions.
     */
    public function ajax_optimize_alt_text(): void {
        // Check nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'aig_nonce')) {
            wp_send_json_error(['message' => 'Invalid security token.']);
            return;
        }

        // Check permissions
        if (!current_user_can('upload_files')) {
            wp_send_json_error(['message' => 'You do not have permission to optimize alt text.']);
            return;
        }

        // Get attachment ID if provided
        $attachment_id = isset($_POST['attachment_id']) ? intval($_POST['attachment_id']) : 0;

        if ($attachment_id > 0) {
            // Optimize a single attachment
            $success = $this->generate_alt_text_and_caption($attachment_id);

            if ($success) {
                wp_send_json_success(['message' => 'Alt text and caption optimized successfully.']);
            } else {
                wp_send_json_error(['message' => 'Failed to optimize alt text and caption.']);
            }
        } else {
            // Start bulk optimization
            $this->start_bulk_alt_text_optimization();
            wp_send_json_success(['message' => 'Bulk optimization started. This may take some time.']);
        }
    }

    /**
     * Start bulk optimization of alt text and captions.
     */
    private function start_bulk_alt_text_optimization(): void {
        // Get all image attachments
        $args = [
            'post_type' => 'attachment',
            'post_mime_type' => 'image',
            'post_status' => 'inherit',
            'posts_per_page' => -1,
            'fields' => 'ids',
        ];

        $attachment_ids = get_posts($args);

        if (empty($attachment_ids)) {
            return;
        }

        // Process in batches using Action Scheduler if available
        if (function_exists('as_enqueue_async_action')) {
            // Split into batches of 10
            $batches = array_chunk($attachment_ids, 10);

            foreach ($batches as $batch) {
                as_enqueue_async_action('aig_optimize_alt_text_batch', ['attachment_ids' => $batch], 'aig-alt-text');
            }
        } else {
            // Fallback to WP Cron
            foreach ($attachment_ids as $attachment_id) {
                wp_schedule_single_event(time() + rand(10, 300), 'aig_optimize_alt_text_single', ['attachment_id' => $attachment_id]);
            }
        }
    }

    /**
     * Process a batch of attachments for alt text optimization.
     *
     * @param array $attachment_ids Array of attachment IDs
     */
    public function process_alt_text_batch(array $attachment_ids): void {
        if (empty($attachment_ids)) {
            return;
        }

        foreach ($attachment_ids as $attachment_id) {
            $this->generate_alt_text_and_caption($attachment_id);

            // Add a small delay to avoid API rate limits
            usleep(500000); // 0.5 seconds
        }
    }

    /**
     * Process a single attachment for alt text optimization.
     *
     * @param int $attachment_id Attachment ID
     */
    public function process_alt_text_single(int $attachment_id): void {
        $this->generate_alt_text_and_caption($attachment_id);
    }

    /**
     * Find an image with AI using semantic search.
     *
     * @param string $text Text to find an image for
     * @param int $limit Maximum number of images to consider
     * @return int|null Attachment ID of the best matching image or null if none found
     */
    public function find_image_with_ai(string $text, int $limit = 5): ?int {
        if (!$this->use_semantic_search) {
            // Fallback to keyword-based search if semantic search is disabled
            return $this->find_image_with_keywords($text);
        }

        $similar_images = $this->find_similar_images($text, $limit, $this->image_match_threshold);

        if (empty($similar_images)) {
            // Fallback to keyword-based search if no similar images found
            return $this->find_image_with_keywords($text);
        }

        // Return the ID of the most similar image
        return (int) array_key_first($similar_images);
    }

    /**
     * Find an image using keyword-based search (fallback method).
     *
     * @param string $text Text to extract keywords from
     * @return int|null Attachment ID of the best matching image or null if none found
     */
    private function find_image_with_keywords(string $text): ?int {
        // Extract keywords from text
        $keywords = $this->extract_keywords($text);

        if (empty($keywords)) {
            return null;
        }

        // Get candidate images based on keywords
        $candidate_images = $this->get_candidate_images($keywords);

        if (empty($candidate_images)) {
            return null;
        }

        // If only one candidate, return it
        if (count($candidate_images) === 1) {
            return $candidate_images[0];
        }

        // Otherwise, use Gemini to pick the best match
        return $this->pick_best_image_with_gemini($text, $candidate_images);
    }

    // --- Field Renderers (Using single option array structure) ---

    public function render_api_key_field(): void {
        printf(
            '<input type="password" name="%s[aig_api_key]" value="%s" class="regular-text" placeholder="%s" autocomplete="off">',
            esc_attr(AIG_PRO_OPTION_GROUP),
            esc_attr($this->api_key),
            __('Enter your Gemini API Key', 'auto-image-generator-pro')
        );
        echo '<p class="description">' . sprintf(
            wp_kses(
                __('Get your key from <a href="%s" target="_blank" rel="noopener noreferrer">Google AI Studio</a>. Ensure the Generative Language API is enabled for your project and billing is active if required.', 'auto-image-generator-pro'),
                ['a' => ['href' => [], 'target' => [], 'rel' => []]]
            ),
            'https://makersuite.google.com/app/apikey' // More direct link
        ) . '</p>';
    }

    public function render_image_style_field(): void {
        $styles = apply_filters('aig_image_styles', [
            'realistic photo' => __('Realistic Photo', 'auto-image-generator-pro'),
            'professional photography' => __('Professional Photography', 'auto-image-generator-pro'),
            'cinematic photography' => __('Cinematic Photography', 'auto-image-generator-pro'),
            'stock photo' => __('Stock Photo', 'auto-image-generator-pro'),
            'photorealistic' => __('Photorealistic', 'auto-image-generator-pro'),
            'illustration' => __('Illustration', 'auto-image-generator-pro'),
            'digital art' => __('Digital Art', 'auto-image-generator-pro'),
            'vector art' => __('Vector Art', 'auto-image-generator-pro'),
            'minimalist design' => __('Minimalist Design', 'auto-image-generator-pro'),
        ]);
        printf('<select name="%s[aig_image_style]" id="aig_image_style">', esc_attr(AIG_PRO_OPTION_GROUP));
        foreach ($styles as $value => $label) {
            printf('<option value="%s" %s>%s</option>', esc_attr($value), selected($this->image_style, $value, false), esc_html($label));
        }
        echo '</select>';
        echo '<p class="description">' . __('Select the default visual style hint for generated images.', 'auto-image-generator-pro') . '</p>';
    }

    public function render_featured_text_style_field(): void {
        $styles = [
            'centered' => __('Centered Composition', 'auto-image-generator-pro'),
            'overlay' => __('Focus on Lower Third', 'auto-image-generator-pro'),
            'banner' => __('Focus on Upper Third', 'auto-image-generator-pro'),
            'minimal' => __('Simple / Minimalist Aesthetic', 'auto-image-generator-pro'),
            'rule_of_thirds' => __('Rule of Thirds Composition', 'auto-image-generator-pro'),
        ];
        printf('<select name="%s[aig_featured_text_style]" id="aig_featured_text_style">', esc_attr(AIG_PRO_OPTION_GROUP));
        foreach ($styles as $value => $label) {
            printf('<option value="%s" %s>%s</option>', esc_attr($value), selected($this->featured_text_style, $value, false), esc_html($label));
        }
        echo '</select>';
        echo '<p class="description">' . __('Adds hints to the featured image prompt for composition (does not add text overlay).', 'auto-image-generator-pro') . '</p>';
    }

    public function render_max_images_field(): void {
        printf(
            '<input type="number" id="aig_max_images" name="%s[aig_max_images]" value="%d" min="1" max="%d" step="1" class="small-text">',
            esc_attr(AIG_PRO_OPTION_GROUP),
            esc_attr($this->max_images),
            AIG_PRO_MAX_IMAGES_LIMIT
        );
        echo '<p class="description">' . sprintf(__('Maximum images to generate per post (1-%d). Requests over %d trigger background processing.', 'auto-image-generator-pro'), AIG_PRO_MAX_IMAGES_LIMIT, $this->background_task_threshold) . '</p>';
    }

    public function render_optimize_images_field(): void {
        printf(
            '<input type="checkbox" id="aig_optimize_images" name="%s[aig_optimize_images]" value="1" %s> <label for="aig_optimize_images">%s</label>',
            esc_attr(AIG_PRO_OPTION_GROUP),
            checked($this->image_optimization_enabled, true, false),
            __('Optimize images using GD (basic compression, resize > 2560px).', 'auto-image-generator-pro')
        );
         echo '<p class="description">' . __('Reduces file size for faster loading. Recommended.', 'auto-image-generator-pro') . '</p>';
    }

    public function render_use_webp_field(): void {
        $disabled = !function_exists('imagewebp') ? 'disabled' : '';
        $message = function_exists('imagewebp')
            ? __('Generate WebP versions alongside originals (requires GD+WebP support).', 'auto-image-generator-pro')
            : __('Requires the GD library with WebP support enabled on your server.', 'auto-image-generator-pro');
        printf(
            '<input type="checkbox" id="aig_use_webp" name="%s[aig_use_webp]" value="1" %s %s> <label for="aig_use_webp">%s</label>',
             esc_attr(AIG_PRO_OPTION_GROUP),
            checked($this->use_webp && function_exists('imagewebp'), true, false),
            $disabled,
            $message
        );
         echo '<p class="description">' . __('WebP offers better compression for modern browsers.', 'auto-image-generator-pro') . '</p>';
    }

    public function render_generate_all_sizes_field(): void {
        printf(
            '<input type="checkbox" id="aig_generate_all_sizes" name="%s[aig_generate_all_sizes]" value="1" %s> <label for="aig_generate_all_sizes">%s</label>',
            esc_attr(AIG_PRO_OPTION_GROUP),
            checked($this->generate_all_sizes, true, false),
             __('Generate standard WP sizes (thumbnail, medium, large, etc.).', 'auto-image-generator-pro')
        );
        echo '<p class="description">' . __('Creates different image dimensions defined by your theme/WP settings. Disable to only save the original generated size.', 'auto-image-generator-pro') . '</p>';
    }

    // Redis support removed to simplify the plugin

    public function render_clear_cache_field(): void {
        echo '<button type="button" id="aig_clear_cache" class="button button-secondary"><span class="dashicons dashicons-trash" style="vertical-align: text-top;"></span> ' . __('Clear AI Image Cache', 'auto-image-generator-pro') . '</button>';
        echo '<span id="aig-cache-status" class="aig-status-inline"></span>'; // Changed class for easier styling
        echo '<p class="description">' . __('Clears cached API results (transients). This forces regeneration if the same prompt is used again. Does not delete images from Media Library.', 'auto-image-generator-pro') . '</p>';
    }

    /**
     * Render the semantic search toggle field.
     */
    public function render_use_semantic_search_field(): void {
        $options = get_option(AIG_PRO_OPTION_GROUP, []);
        $checked = isset($options['aig_use_semantic_search']) ? (bool)$options['aig_use_semantic_search'] : true;

        echo '<label>';
        echo '<input type="checkbox" name="' . AIG_PRO_OPTION_GROUP . '[aig_use_semantic_search]" ' . checked($checked, true, false) . ' value="1" />';
        echo ' ' . __('Enable semantic search for image matching', 'auto-image-generator-pro');
        echo '</label>';
        echo '<p class="description">' . __('Uses AI embeddings to find the most semantically relevant images based on content meaning rather than just keywords.', 'auto-image-generator-pro') . '</p>';

        // Add rebuild index button
        echo '<div style="margin-top: 10px;">';
        echo '<button type="button" id="aig_rebuild_index" class="button button-secondary"><span class="dashicons dashicons-update" style="vertical-align: text-top;"></span> ' . __('Rebuild Embedding Index', 'auto-image-generator-pro') . '</button>';
        echo '<span id="aig-index-status" class="aig-status-inline"></span>';
        echo '</div>';
        echo '<p class="description">' . __('Generates embeddings for all images in your media library. This may take some time for large libraries.', 'auto-image-generator-pro') . '</p>';
    }

    /**
     * Render the semantic match threshold field.
     */
    public function render_image_match_threshold_field(): void {
        $options = get_option(AIG_PRO_OPTION_GROUP, []);
        $value = isset($options['aig_image_match_threshold']) ? (float)$options['aig_image_match_threshold'] : 0.65;
        $value = min(1.0, max(0.1, $value)); // Ensure value is between 0.1 and 1.0

        echo '<input type="range" id="aig_image_match_threshold" name="' . AIG_PRO_OPTION_GROUP . '[aig_image_match_threshold]" min="0.1" max="1.0" step="0.05" value="' . esc_attr($value) . '" style="width: 200px; vertical-align: middle;" />';
        echo '<span id="aig_threshold_value" style="margin-left: 10px; font-weight: bold;">' . $value . '</span>';
        echo '<p class="description">' . __('Minimum similarity score (0.1-1.0) required for an image to be considered a match. Higher values require closer matches.', 'auto-image-generator-pro') . '</p>';

        // Add JavaScript to update the displayed value
        echo '<script>
            document.addEventListener("DOMContentLoaded", function() {
                const slider = document.getElementById("aig_image_match_threshold");
                const display = document.getElementById("aig_threshold_value");

                if (slider && display) {
                    slider.addEventListener("input", function() {
                        display.textContent = this.value;
                    });
                }
            });
        </script>';
    }

    /**
     * Render the AI alt text generation field.
     */
    public function render_alt_text_generation_field(): void {
        $options = get_option(AIG_PRO_OPTION_GROUP, []);
        $checked = isset($options['aig_alt_text_generation']) ? (bool)$options['aig_alt_text_generation'] : true;

        echo '<label>';
        echo '<input type="checkbox" name="' . AIG_PRO_OPTION_GROUP . '[aig_alt_text_generation]" ' . checked($checked, true, false) . ' value="1" />';
        echo ' ' . __('Generate AI-powered alt text and captions', 'auto-image-generator-pro');
        echo '</label>';
        echo '<p class="description">' . __('Automatically generates or enhances alt text and captions for images using AI. Improves accessibility and SEO.', 'auto-image-generator-pro') . '</p>';

        // Add optimize alt text button
        echo '<div style="margin-top: 10px;">';
        echo '<button type="button" id="aig_optimize_alt_text" class="button button-secondary"><span class="dashicons dashicons-admin-accessibility" style="vertical-align: text-top;"></span> ' . __('Optimize Alt Text & Captions', 'auto-image-generator-pro') . '</button>';
        echo '<span id="aig-alt-text-status" class="aig-status-inline"></span>';
        echo '</div>';
        echo '<p class="description">' . __('Bulk optimize alt text and captions for existing images in your media library.', 'auto-image-generator-pro') . '</p>';

        // Add image analysis option
        echo '<div style="margin-top: 15px; padding-top: 15px; border-top: 1px solid #eee;">';
        $this->render_enable_image_analysis_field();
        echo '</div>';
    }

    /**
     * Render the enable image analysis field.
     */
    public function render_enable_image_analysis_field(): void {
        $options = get_option(AIG_PRO_OPTION_GROUP, []);
        $checked = isset($options['aig_enable_image_analysis']) ? (bool)$options['aig_enable_image_analysis'] : false;
        $force_checked = isset($options['aig_force_alt_text_regeneration']) ? (bool)$options['aig_force_alt_text_regeneration'] : false;

        echo '<label>';
        echo '<input type="checkbox" name="' . AIG_PRO_OPTION_GROUP . '[aig_enable_image_analysis]" ' . checked($checked, true, false) . ' value="1" />';
        echo ' ' . __('Enable AI image content analysis', 'auto-image-generator-pro');
        echo '</label>';
        echo '<p class="description">' . __('Uses AI to analyze image content for better alt text and caption generation. May increase API usage.', 'auto-image-generator-pro') . '</p>';

        echo '<div style="margin-top: 10px; margin-left: 20px;">';
        echo '<label>';
        echo '<input type="checkbox" name="' . AIG_PRO_OPTION_GROUP . '[aig_force_alt_text_regeneration]" ' . checked($force_checked, true, false) . ' value="1" />';
        echo ' ' . __('Force regeneration of existing alt text', 'auto-image-generator-pro');
        echo '</label>';
        echo '<p class="description">' . __('When enabled, will regenerate alt text and captions even if they already exist. Use with caution as this will overwrite existing content.', 'auto-image-generator-pro') . '</p>';
        echo '</div>';
    }

    /**
     * Embed CSS and JS directly into the admin head for relevant pages.
     * Uses cache busting version string.
     * Improved CSS for better visual appeal and layout.
     */
    public function embed_admin_scripts_and_styles(): void {
        $screen = get_current_screen();
        if (!$screen || !in_array($screen->id, ['toplevel_page_' . AIG_PRO_SETTINGS_SLUG, 'post', 'dashboard'], true)) {
            return;
        }

        // Nonce for JS AJAX calls
        $aig_nonce = wp_create_nonce('aig_nonce');
        $aig_clear_cache_nonce = wp_create_nonce('aig_clear_cache');

        // Translation strings for JS
        $js_strings = [
            'generating' => __('Generating...', 'auto-image-generator-pro'),
            'queued' => __('✅ Queued! %d images scheduled.', 'auto-image-generator-pro'),
            'success' => __('✅ Success! %d images added.', 'auto-image-generator-pro'),
            'failed' => __('❌ Failed!', 'auto-image-generator-pro'),
            'error' => __('Error:', 'auto-image-generator-pro'),
            'unknownError' => __('Unknown error occurred.', 'auto-image-generator-pro'),
            'networkError' => __('Network error. Please check console and try again.', 'auto-image-generator-pro'),
            'generateImages' => __('Generate Images', 'auto-image-generator-pro'),
            'clearing' => __('Clearing...', 'auto-image-generator-pro'),
            'cacheCleared' => __('Cache Cleared!', 'auto-image-generator-pro'),
            'errorClearing' => __('Error Clearing Cache', 'auto-image-generator-pro'),
            'clearImageCache' => __('Clear AI Image Cache', 'auto-image-generator-pro'),
            'processing' => __('Processing...', 'auto-image-generator-pro'),
            'queuedNotice' => __('✅ Queued! %d images scheduled. Check admin notices later.', 'auto-image-generator-pro'),
            'successReload' => __('✅ Success! %d images added. Reloading page...', 'auto-image-generator-pro'),
            'errorUnknown' => __('Error: Unknown error.', 'auto-image-generator-pro'),
            'networkErrorRetry' => __('Network error. Please try again.', 'auto-image-generator-pro'),
            'viewPost' => __('View Post', 'auto-image-generator-pro'),
            'ago' => __('ago', 'auto-image-generator-pro'),
            'started' => __('Started!', 'auto-image-generator-pro'),
            'rebuildIndex' => __('Rebuild Embedding Index', 'auto-image-generator-pro'),
            'optimizeAltText' => __('Optimize Alt Text & Captions', 'auto-image-generator-pro'),
            'apiKeyMissing' => __('API Key not set in Auto Images settings.', 'auto-image-generator-pro'),
            'headingsFound' => __('%d headings found.', 'auto-image-generator-pro'),
            'exists' => __('exists', 'auto-image-generator-pro'),
             'serverError' => __('Server error', 'auto-image-generator-pro'),
             'generatingInline' => __('Generating...', 'auto-image-generator-pro'), // Shorter version for inline
             'imagesLabel' => __('Images:', 'auto-image-generator-pro'),
             'numImagesTitle' => __('Number of images to generate', 'auto-image-generator-pro'),
             'featuredOnlyTitle' => __('Only generate and set a featured image', 'auto-image-generator-pro'),
             'featuredOnlyLabel' => __('Featured image only', 'auto-image-generator-pro'),
             'editPostTitle' => __('Edit Post', 'auto-image-generator-pro'),
             'headingsCountTooltip' => __('%d H2 headings found in content', 'auto-image-generator-pro'),
             'headingsCountLabel' => __('%d headings', 'auto-image-generator-pro'),
             'featuredImageExistsTooltip' => __('Post already has a featured image', 'auto-image-generator-pro'),
             'listLimitNote' => __('List limited to the latest %d posts needing images.', 'auto-image-generator-pro'),
        ];

        // Embedded CSS (Improved and Organized)
        ?>
        <style>
            /* General Plugin Styles */
            .aig-wrap .notice:not(.inline):not(.notice-error):not(.notice-warning):not(.notice-success):not(.notice-info) { display: none; } /* Hide default WP notices on our page unless specific */
            .aig-card { background: #fff; border: 1px solid #ccd0d4; box-shadow: 0 1px 2px rgba(0,0,0,.07); margin: 25px 0; border-radius: 4px; }
            .aig-card h2 { font-size: 1.4em; margin: 0; padding: 15px 20px; border-bottom: 1px solid #eee; display: flex; align-items: center; gap: 8px; font-weight: 600; }
            .aig-card h2 .dashicons { color: #007cba; }
            .aig-card .inside { padding: 5px 20px 20px; }
            .aig-card form > p { padding: 0 20px 20px; } /* Padding for description under section */
            .form-table th { width: 200px; }
            .form-table td p.description { font-size: 0.9em; color: #666; }
            #aig_clear_cache { margin-right: 10px; }

            /* Post List Styles */
            .aig-post-row { background: #f8f9fa; padding: 15px 20px; margin-bottom: 12px; border: 1px solid #e0e0e0; border-radius: 4px; display: flex; flex-wrap: wrap; justify-content: space-between; align-items: center; gap: 15px; transition: opacity 0.5s ease-out; }
            .aig-post-title { font-weight: 600; font-size: 15px; flex: 1 1 300px; /* Allow wrapping */ }
            .aig-post-title a { text-decoration: none; color: #007cba; }
            .aig-post-title a:hover { color: #005a87; }
            .aig-post-meta { font-size: 0.9em; color: #555; margin-left: 8px; font-weight: 400; display: inline-flex; align-items: center; gap: 5px; }
            .aig-post-meta .dashicons { font-size: 18px; vertical-align: sub; }
            .aig-post-meta .dashicons-format-image { color: #78c8e6; } /* Icon for existing featured image */

            /* Post Action Styles */
            .aig-post-actions { display: flex; gap: 15px; flex-wrap: wrap; align-items: center; flex: 1 1 400px; justify-content: flex-end; }
            .aig-post-actions label { cursor: pointer; display: inline-flex; align-items: center; gap: 5px; font-size: 0.95em; }
            .aig-post-actions .aig-image-count { width: 65px; padding: 4px 6px; }
            .aig-post-actions .button.aig-generate-btn { min-width: 140px; text-align: center; }
            .aig-post-actions .button.aig-generate-btn.aig-success { background-color: #28a745; border-color: #28a745; color: #fff; }
            .aig-post-actions .button.aig-generate-btn.aig-error { background-color: #dc3545; border-color: #dc3545; color: #fff; }

            /* Status Indicators & Loading */
            .aig-status-inline { margin-left: 8px; display: inline-flex; align-items: center; vertical-align: middle; }
            .aig-status-indicator { min-width: 24px; text-align: center; margin-left: 5px; display: inline-block; vertical-align: middle; }
            .aig-status-indicator .dashicons { font-size: 20px; vertical-align: middle; }
            .aig-success .dashicons-yes-alt { color: #28a745; }
            .aig-error .dashicons-warning { color: #dc3545; }
            .aig-loading { display: inline-block; width: 16px; height: 16px; border: 2px solid rgba(0,115,170,.3); border-radius: 50%; border-top-color: #0073aa; animation: aig-spin .8s linear infinite; margin: 0 5px 0 0; vertical-align: middle; }
            @keyframes aig-spin { to { transform: rotate(360deg); } }

            /* Tooltip */
            .aig-tooltip { position: relative; cursor: help; border-bottom: 1px dotted #777; }
            .aig-tooltip:hover::after { content: attr(data-tooltip); position: absolute; bottom: 120%; left: 50%; transform: translateX(-50%); background: #333; color: #fff; padding: 6px 12px; border-radius: 4px; white-space: nowrap; font-size: 12px; z-index: 1001; opacity: .95; box-shadow: 0 2px 5px rgba(0,0,0,0.2); }

            /* Admin Notices */
            .aig-notice-background { border-left-color: #007cba !important; }
            .aig-notice-background strong { color: #005a87; }
            .aig-notice-background .dashicons-edit { vertical-align: text-bottom; margin-right: 2px;}

            /* Dashboard Widget */
            #aig_dashboard_widget .inside { padding: 15px; }
            #aig_dashboard_widget ul { list-style-type: none; padding-left: 0; margin: 10px 0 15px; }
            #aig_dashboard_widget ul li { margin-bottom: 5px; padding-left: 20px; position: relative; }
            #aig_dashboard_widget ul li::before { content: "\f105"; font-family: dashicons; position: absolute; left: 0; top: 1px; color: #007cba; }
            #aig_dashboard_widget .aig-widget-status { margin-bottom: 15px; padding: 10px; border-radius: 3px; display: flex; align-items: center; gap: 8px; }
            #aig_dashboard_widget .aig-widget-status.needs-images { background-color: #fff8e5; border-left: 4px solid #ffb900; }
            #aig_dashboard_widget .aig-widget-status.all-good { background-color: #e5f5e5; border-left: 4px solid #28a745; }
            #aig_dashboard_widget .aig-widget-status .dashicons { font-size: 24px; }
            #aig_dashboard_widget .aig-widget-status.needs-images .dashicons-warning { color:#ffb900; }
            #aig_dashboard_widget .aig-widget-status.all-good .dashicons-yes-alt { color:#28a745; }
            #aig_dashboard_widget .button-primary { margin-right: 5px; }
            #aig_dashboard_widget .widget-footer { margin-top: 15px; border-top: 1px solid #eee; padding-top: 10px; font-size: 0.9em; color: #777; }

            /* Meta Box */
            #aig-generate-metabox .inside { padding: 12px; }
            #aig-meta-box-content label { display: block; margin-bottom: 10px; }
            #aig-meta-box-content select, #aig-meta-box-content input[type=checkbox] { margin-left: 5px; }
            #aig-meta-box-content button { margin-top: 12px; width: 100%; text-align: center; }
            #aig-status-metabox { margin-top: 10px; padding: 8px; border-radius: 3px; font-size: 0.95em; display: flex; align-items: center; gap: 5px; background: #f0f0f1; border: 1px solid #dcdcde; }
            #aig-status-metabox .spinner { float: none; margin: 0; visibility: visible; }
            #aig-status-metabox .aig-success-msg { color: #135e15; }
            #aig-status-metabox .aig-error-msg { color: #d63638; }
            #aig-status-metabox .aig-queued-msg { color: #007cba; }
        </style>
        <?php

        // Embedded JS (Refined, using localized strings)
        ?>
        <script>
            document.addEventListener('DOMContentLoaded', function() {
                const ajaxurl = '<?php echo esc_url(admin_url('admin-ajax.php')); ?>';
                const aigNonce = '<?php echo esc_js($aig_nonce); ?>';
                const aigClearCacheNonce = '<?php echo esc_js($aig_clear_cache_nonce); ?>';
                const i18n = <?php echo wp_json_encode($js_strings); ?>;

                // Helper function to escape attributes in JS
                function escAttr(str) {
                    if (!str) return '';
                    const map = { '&': '&amp;', '<': '&lt;', '>': '&gt;', '"': '&quot;', "'": '&#039;' };
                    return String(str).replace(/[&<>"']/g, function(s) { return map[s]; });
                }

                // Helper to format string like sprintf
                function sprintf(format, ...args) {
                    let i = 0;
                    return format.replace(/%[sd]/g, (match) => {
                        const arg = args[i++];
                        return typeof arg !== 'undefined' ? String(arg) : match;
                    });
                }

                // --- Settings Page: Generate Button Handler ---
                document.querySelectorAll('.aig-generate-btn').forEach(button => {
                    button.addEventListener('click', function(e) {
                        e.preventDefault();
                        const btn = this;
                        const postRow = btn.closest('.aig-post-row');
                        if (!postRow) return;
                        const postId = btn.dataset.postId;
                        const countSelect = postRow.querySelector('.aig-image-count');
                        const featuredOnlyCheckbox = postRow.querySelector('.aig-featured-only');
                        const statusIndicator = postRow.querySelector('.aig-status-indicator');

                        const count = countSelect ? countSelect.value : 1;
                        const featuredOnly = featuredOnlyCheckbox ? featuredOnlyCheckbox.checked : false;

                        btn.disabled = true;
                        btn.innerHTML = `<span class="aig-loading"></span> ${i18n.generatingInline}`;
                        if (statusIndicator) statusIndicator.innerHTML = '<span class="aig-loading"></span>'; // Show loading in indicator too

                        const formData = new FormData();
                        formData.append('action', 'aig_generate_images');
                        formData.append('post_id', postId);
                        formData.append('count', count);
                        formData.append('featured_only', featuredOnly);
                        formData.append('nonce', aigNonce);

                        fetch(ajaxurl, { method: 'POST', body: formData })
                        .then(response => {
                            if (!response.ok) {
                                // Handle HTTP errors (like 500 Internal Server Error)
                                throw new Error(`HTTP error! status: ${response.status}`);
                            }
                            return response.json();
                        })
                        .then(data => {
                            if (data.success) {
                                const isBackground = data.data.background;
                                const generatedCount = data.data.count || 0;
                                const msg = isBackground ? sprintf(i18n.queued, generatedCount) : sprintf(i18n.success, generatedCount);

                                btn.innerHTML = msg;
                                btn.classList.add('aig-success'); btn.classList.remove('aig-error');
                                if (statusIndicator) statusIndicator.innerHTML = '<span class="aig-success"><span class="dashicons dashicons-yes-alt"></span></span>';

                                if (!isBackground && postRow) {
                                    // Fade out successful row if processed immediately
                                    postRow.style.transition = 'opacity 0.8s ease-out';
                                    setTimeout(() => { postRow.style.opacity = '0'; setTimeout(() => postRow.remove(), 800); }, 2000);
                                } else {
                                    // Reset button after a delay for background tasks
                                    setTimeout(() => {
                                        btn.innerHTML = i18n.generateImages;
                                        btn.disabled = false; btn.classList.remove('aig-success');
                                         if (statusIndicator) statusIndicator.innerHTML = ''; // Clear status
                                    }, 4000);
                                }
                            } else {
                                // Handle WP JSON error
                                throw new Error(data.data || i18n.unknownError);
                            }
                        })
                        .catch(error => {
                            // Handle fetch errors and WP JSON errors caught above
                            btn.innerHTML = i18n.failed;
                            btn.classList.add('aig-error'); btn.classList.remove('aig-success');
                            const errorMsg = error.message || i18n.serverError;
                            if (statusIndicator) statusIndicator.innerHTML = `<span class="aig-error" title="${escAttr(errorMsg)}"><span class="dashicons dashicons-warning"></span></span>`;
                            console.error('AIG Error:', error);
                            alert(`${i18n.error} ${errorMsg}`);
                            setTimeout(() => {
                                btn.innerHTML = i18n.generateImages;
                                btn.disabled = false; btn.classList.remove('aig-error');
                                if (statusIndicator) statusIndicator.innerHTML = ''; // Clear status
                            }, 4000);
                        });
                    });
                });

                // Redis support removed to simplify the plugin

                // --- Settings Page: Clear Cache Button ---
                const clearCacheButton = document.getElementById('aig_clear_cache');
                const cacheStatus = document.getElementById('aig-cache-status');
                if (clearCacheButton && cacheStatus) {
                    clearCacheButton.addEventListener('click', function() {
                        const btn = this;
                        btn.disabled = true;
                        btn.innerHTML = `<span class="aig-loading"></span> ${i18n.clearing}`;
                        cacheStatus.innerHTML = '<span class="aig-loading"></span>';

                        const formData = new FormData();
                        formData.append('action', 'aig_clear_cache');
                        formData.append('nonce', aigClearCacheNonce);

                        fetch(ajaxurl, { method: 'POST', body: formData })
                        .then(response => response.json())
                        .then(data => {
                            if (data.success) {
                                btn.innerHTML = `<span class="dashicons dashicons-yes" style="color:green; vertical-align: text-top;"></span> ${i18n.cacheCleared}`;
                                cacheStatus.innerHTML = '<span class="aig-success"><span class="dashicons dashicons-yes-alt"></span></span>';
                            } else {
                                throw new Error(data.data || i18n.errorClearing);
                            }
                        })
                        .catch(error => {
                            btn.innerHTML = `<span class="dashicons dashicons-warning" style="color:red; vertical-align: text-top;"></span> ${i18n.errorClearing}`;
                            cacheStatus.innerHTML = `<span class="aig-error" title="${escAttr(error.message)}"><span class="dashicons dashicons-warning"></span></span>`;
                            console.error('Cache Clear Error:', error);
                        })
                        .finally(() => {
                             setTimeout(() => {
                                btn.innerHTML = `<span class="dashicons dashicons-trash" style="vertical-align: text-top;"></span> ${i18n.clearImageCache}`;
                                btn.disabled = false;
                                cacheStatus.innerHTML = ''; // Clear status icon
                             }, 3000);
                        });
                    });
                }

                // --- Settings Page: Rebuild Embedding Index Button ---
                const rebuildIndexButton = document.getElementById('aig_rebuild_index');
                const indexStatus = document.getElementById('aig-index-status');
                if (rebuildIndexButton && indexStatus) {
                    rebuildIndexButton.addEventListener('click', function() {
                        const btn = this;
                        btn.disabled = true;
                        btn.innerHTML = `<span class="aig-loading"></span> ${i18n.processing}`;
                        indexStatus.innerHTML = '<span class="aig-loading"></span>';

                        const formData = new FormData();
                        formData.append('action', 'aig_rebuild_index');
                        formData.append('nonce', aigNonce);

                        fetch(ajaxurl, { method: 'POST', body: formData })
                        .then(response => response.json())
                        .then(data => {
                            if (data.success) {
                                btn.innerHTML = `<span class="dashicons dashicons-yes" style="color:green; vertical-align: text-top;"></span> ${i18n.started}`;
                                indexStatus.innerHTML = '<span class="aig-success"><span class="dashicons dashicons-yes-alt"></span></span>';
                            } else {
                                throw new Error(data.data?.message || i18n.errorUnknown);
                            }
                        })
                        .catch(error => {
                            btn.innerHTML = `<span class="dashicons dashicons-warning" style="color:red; vertical-align: text-top;"></span> ${i18n.error}`;
                            indexStatus.innerHTML = `<span class="aig-error" title="${escAttr(error.message)}"><span class="dashicons dashicons-warning"></span></span>`;
                            console.error('Rebuild Index Error:', error);
                        })
                        .finally(() => {
                             setTimeout(() => {
                                btn.innerHTML = `<span class="dashicons dashicons-update" style="vertical-align: text-top;"></span> ${i18n.rebuildIndex}`;
                                btn.disabled = false;
                                indexStatus.innerHTML = ''; // Clear status icon
                             }, 3000);
                        });
                    });
                }

                // --- Settings Page: Optimize Alt Text Button ---
                const optimizeAltTextButton = document.getElementById('aig_optimize_alt_text');
                const altTextStatus = document.getElementById('aig-alt-text-status');
                if (optimizeAltTextButton && altTextStatus) {
                    optimizeAltTextButton.addEventListener('click', function() {
                        const btn = this;
                        btn.disabled = true;
                        btn.innerHTML = `<span class="aig-loading"></span> ${i18n.processing}`;
                        altTextStatus.innerHTML = '<span class="aig-loading"></span>';

                        const formData = new FormData();
                        formData.append('action', 'aig_optimize_alt_text');
                        formData.append('nonce', aigNonce);

                        fetch(ajaxurl, { method: 'POST', body: formData })
                        .then(response => response.json())
                        .then(data => {
                            if (data.success) {
                                btn.innerHTML = `<span class="dashicons dashicons-yes" style="color:green; vertical-align: text-top;"></span> ${i18n.started}`;
                                altTextStatus.innerHTML = '<span class="aig-success"><span class="dashicons dashicons-yes-alt"></span></span>';
                            } else {
                                throw new Error(data.data?.message || i18n.errorUnknown);
                            }
                        })
                        .catch(error => {
                            btn.innerHTML = `<span class="dashicons dashicons-warning" style="color:red; vertical-align: text-top;"></span> ${i18n.error}`;
                            altTextStatus.innerHTML = `<span class="aig-error" title="${escAttr(error.message)}"><span class="dashicons dashicons-warning"></span></span>`;
                            console.error('Optimize Alt Text Error:', error);
                        })
                        .finally(() => {
                             setTimeout(() => {
                                btn.innerHTML = `<span class="dashicons dashicons-admin-accessibility" style="vertical-align: text-top;"></span> ${i18n.optimizeAltText}`;
                                btn.disabled = false;
                                altTextStatus.innerHTML = ''; // Clear status icon
                             }, 3000);
                        });
                    });
                }

                // --- Post Edit Screen: Meta Box Generate Button ---
                const metaBoxButton = document.getElementById('aig-generate-btn-metabox');
                const metaBoxStatus = document.getElementById('aig-status-metabox');
                const metaBoxCount = document.getElementById('aig-count-metabox');
                const metaBoxFeaturedOnly = document.getElementById('aig-featured-only-metabox');
                const metaBoxPostIdInput = document.getElementById('aig_post_id_metabox'); // Renamed for clarity

                if (metaBoxButton && metaBoxStatus && metaBoxCount && metaBoxFeaturedOnly && metaBoxPostIdInput) {
                    metaBoxButton.addEventListener('click', function(e) {
                        e.preventDefault();
                        const btn = this;
                        const postId = metaBoxPostIdInput.value;
                        const count = metaBoxCount.value;
                        const featuredOnly = metaBoxFeaturedOnly.checked;

                        btn.disabled = true;
                        btn.textContent = i18n.generating;
                        metaBoxStatus.innerHTML = `<span class="spinner is-active"></span> ${i18n.processing}`;
                        metaBoxStatus.className = 'aig-status-metabox'; // Reset status class

                        const formData = new FormData();
                        formData.append('action', 'aig_generate_images');
                        formData.append('post_id', postId);
                        formData.append('count', count);
                        formData.append('featured_only', featuredOnly);
                        formData.append('nonce', aigNonce);

                        fetch(ajaxurl, { method: 'POST', body: formData })
                        .then(response => {
                            if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
                            return response.json();
                        })
                        .then(data => {
                            if (data.success) {
                                const isBackground = data.data.background;
                                const generatedCount = data.data.count || 0;
                                let msg = '';
                                let statusClass = '';

                                if (isBackground) {
                                    msg = sprintf(i18n.queuedNotice, generatedCount);
                                    statusClass = 'aig-queued-msg';
                                } else {
                                    msg = sprintf(i18n.successReload, generatedCount);
                                    statusClass = 'aig-success-msg';
                                }

                                metaBoxStatus.innerHTML = `<span class="${statusClass}">${msg}</span>`;
                                metaBoxStatus.classList.add(statusClass.split('-msg')[0]); // Add base class e.g. 'aig-success'

                                if (!isBackground) {
                                    // Reload after success if not background
                                    setTimeout(() => { location.reload(); }, 2500);
                                } else {
                                    // Re-enable button for background tasks
                                    btn.disabled = false;
                                    btn.textContent = i18n.generateImages;
                                }
                            } else {
                                throw new Error(data.data || i18n.unknownError);
                            }
                        })
                        .catch(error => {
                            const errorMsg = error.message || i18n.networkErrorRetry;
                            metaBoxStatus.innerHTML = `<span class="aig-error-msg">❌ ${i18n.error} ${escAttr(errorMsg)}</span>`;
                            metaBoxStatus.classList.add('aig-error');
                            btn.disabled = false;
                            btn.textContent = i18n.generateImages;
                            console.error('AIG Meta Box Error:', error);
                        });
                    });
                }
            });
        </script>
        <?php
    }

    /**
     * Render the main admin settings page.
     * Uses styled cards for better organization.
     */
    public function render_admin_page(): void {
        $this->load_options(); // Ensure latest options are loaded for display
        ?>
        <div class="wrap aig-wrap">
            <h1>
                <span class="dashicons dashicons-camera-alt" style="font-size: 1.2em; margin-right: 5px; color: #007cba;"></span>
                <?php esc_html_e('Auto Image Generator Pro', 'auto-image-generator-pro'); ?>
                <span style="font-size: 0.6em; color: #777; vertical-align: middle; margin-left: 5px;">v<?php echo esc_html(AIG_PRO_VERSION); ?></span>
            </h1>

            <?php if (empty($this->api_key)): ?>
            <div class="notice notice-warning is-dismissible">
                <p><strong><?php esc_html_e('Action Required:', 'auto-image-generator-pro'); ?></strong> <?php esc_html_e('Please set your Gemini API key below to enable image generation.', 'auto-image-generator-pro'); ?></p>
            </div>
            <?php endif; ?>

            <?php settings_errors(); // Display any settings update errors/notices ?>

            <form method="post" action="options.php">
                <?php settings_fields(AIG_PRO_OPTION_GROUP); ?>

                <div class="aig-card">
                    <h2><span class="dashicons dashicons-admin-generic"></span> <?php esc_html_e('Settings', 'auto-image-generator-pro'); ?></h2>
                    <div class="inside">
                        <?php do_settings_sections(AIG_PRO_SETTINGS_SLUG); ?>
                    </div>
                     <?php submit_button(__('Save All Settings', 'auto-image-generator-pro')); ?>
                </div>
            </form> <?php // End Settings Form ?>


            <div class="aig-card">
                 <h2><span class="dashicons dashicons-admin-post"></span> <?php esc_html_e('Posts Possibly Needing Images', 'auto-image-generator-pro'); ?></h2>
                 <div class="inside">
                    <?php
                    $limit_posts_scan = 25; // Slightly increase scan limit
                    $posts_without_images = $this->get_posts_without_images($limit_posts_scan);

                    if (empty($posts_without_images)) {
                        echo '<p><span class="dashicons dashicons-yes-alt" style="color:green;"></span> ' . esc_html__('Excellent! All recent published posts seem to have images.', 'auto-image-generator-pro') . '</p>';
                    } else {
                        echo '<p>' . sprintf(esc_html__('Found %d posts (out of the last %d scanned) that might need images. Generate a featured image and/or content images based on title/headings.', 'auto-image-generator-pro'), count($posts_without_images), $limit_posts_scan) . '</p>';

                        foreach ($posts_without_images as $post) {
                            $post_id = $post->ID;
                            $post_title = get_the_title($post_id);
                            $edit_link = get_edit_post_link($post_id);
                            $has_thumbnail = has_post_thumbnail($post_id);

                            // Efficiently count H2 headings
                            $heading_count = substr_count(strtolower($post->post_content), '<h2');
                            ?>
                            <div class="aig-post-row">
                                <div class="aig-post-title">
                                    <?php if ($edit_link): ?>
                                    <a href="<?php echo esc_url($edit_link); ?>" target="_blank" title="<?php echo esc_attr_x('Edit Post', 'js translation', 'auto-image-generator-pro'); ?>">
                                        <?php echo esc_html(wp_trim_words($post_title, 15, '...')); ?>
                                    </a>
                                    <?php else: ?>
                                        <?php echo esc_html(wp_trim_words($post_title, 15, '...')); ?>
                                    <?php endif; ?>

                                    <span class="aig-post-meta">
                                        <?php if ($heading_count > 0): ?>
                                            <span class="aig-tooltip" data-tooltip="<?php printf(esc_attr_x('%d H2 headings found in content', 'js translation', 'auto-image-generator-pro'), $heading_count); ?>">
                                                <span class="dashicons dashicons-editor-ul"></span> <?php echo esc_html($heading_count); ?>
                                            </span>
                                        <?php endif; ?>
                                        <?php if ($has_thumbnail): ?>
                                            <span class="aig-tooltip" data-tooltip="<?php echo esc_attr_x('Post already has a featured image', 'js translation', 'auto-image-generator-pro'); ?>">
                                                <span class="dashicons dashicons-format-image"></span>
                                            </span>
                                        <?php endif; ?>
                                    </span>
                                </div>
                                <div class="aig-post-actions">
                                    <label>
                                        <?php echo esc_html_x('Images:', 'js translation', 'auto-image-generator-pro'); ?>
                                        <select class="aig-image-count small-text" title="<?php echo esc_attr_x('Number of images to generate', 'js translation', 'auto-image-generator-pro'); ?>">
                                            <?php for ($i = 1; $i <= $this->max_images; $i++): ?>
                                            <option value="<?php echo $i; ?>" <?php selected($i, 1); ?>><?php echo $i; ?></option>
                                            <?php endfor; ?>
                                        </select>
                                    </label>
                                    <label title="<?php echo esc_attr_x('Only generate and set a featured image', 'js translation', 'auto-image-generator-pro'); ?>">
                                        <input type="checkbox" class="aig-featured-only" <?php checked(!$has_thumbnail); ?>>
                                        <?php echo esc_html_x('Featured only', 'Label for featured image checkbox', 'auto-image-generator-pro'); ?>
                                    </label>
                                    <button class="button button-primary aig-generate-btn" data-post-id="<?php echo esc_attr($post_id); ?>">
                                        <?php echo esc_html_x('Generate Images', 'js translation', 'auto-image-generator-pro'); ?>
                                    </button>
                                    <span class="aig-status-indicator"></span>
                                </div>
                            </div>
                            <?php
                        }
                         echo '<p><small>' . sprintf(esc_html_x('List limited to the latest %d posts needing images.', 'js translation', 'auto-image-generator-pro'), $limit_posts_scan) . '</small></p>';
                    }
                    ?>
                </div>
            </div>
        </div>
        <?php
    }

    // --- Core Logic: Image Generation ---

    /**
     * AJAX handler for generating images.
     * Validates input, checks API key, and either processes immediately or schedules a background task.
     */
    public function ajax_generate_images(): void {
        // Add a global try-catch around the entire function to catch any uncaught errors
        try {
            try {
                error_log("[AIG Pro AJAX] Starting ajax_generate_images handler");

                check_ajax_referer('aig_nonce', 'nonce');

            if (!current_user_can('manage_options')) {
                error_log("[AIG Pro AJAX] Permission denied - user cannot manage options");
                wp_send_json_error(__('Permission denied.', 'auto-image-generator-pro'), 403);
                return;
            }

            if (empty($this->api_key)) {
                error_log("[AIG Pro AJAX] API key is empty");
                wp_send_json_error(__('Please configure your Gemini API key in the plugin settings.', 'auto-image-generator-pro'), 400);
                return;
            }

            error_log("[AIG Pro AJAX] Passed initial validation checks");
        } catch (\Throwable $t) {
            error_log("[AIG Pro AJAX Error] Exception in initial validation: " . $t->getMessage());
            wp_send_json_error("Internal server error: " . $t->getMessage(), 500);
            return;
        }

        try {
            error_log("[AIG Pro AJAX] Processing post parameters");

            $post_id = isset($_POST['post_id']) ? absint($_POST['post_id']) : 0;
            $count = isset($_POST['count']) ? min($this->max_images, max(1, absint($_POST['count']))) : 1;
            $featured_only = isset($_POST['featured_only']) && filter_var($_POST['featured_only'], FILTER_VALIDATE_BOOLEAN, FILTER_NULL_ON_FAILURE) ?? false;

            error_log("[AIG Pro AJAX] Parameters: post_id={$post_id}, count={$count}, featured_only=" . ($featured_only ? 'true' : 'false'));

            $post = get_post($post_id);
            $supported_types = apply_filters('aig_supported_post_types', ['post']);

            if (!$post || !in_array($post->post_type, $supported_types, true)) {
                error_log("[AIG Pro AJAX] Invalid post ID or unsupported post type: " . ($post ? $post->post_type : 'null'));
                wp_send_json_error(__('Invalid post ID or unsupported post type.', 'auto-image-generator-pro'), 404);
                return;
            }
        } catch (\Throwable $t) {
            error_log("[AIG Pro AJAX Error] Exception in post validation: " . $t->getMessage());
            wp_send_json_error("Internal server error in post validation: " . $t->getMessage(), 500);
            return;
        }

        // Use background processing for requests exceeding the threshold
        if ($this->background_tasks_enabled && $count > $this->background_task_threshold) {
            $user_id = get_current_user_id();

            // Use our unified scheduling method
            $scheduled = $this->schedule_post_processing($post_id, $count, $featured_only, $user_id);

            if ($scheduled) {
                wp_send_json_success([
                    'message' => __('Images scheduled for generation in the background.', 'auto-image-generator-pro'),
                    'background' => true,
                    'count' => $count
                ]);
            } else {
                // Check if already scheduled
                if (function_exists('as_next_scheduled_action')) {
                    $existing_action = as_next_scheduled_action(
                        'aig_process_post_action_scheduler',
                        [
                            'post_id' => $post_id,
                            'count' => $count,
                            'featured_only' => $featured_only,
                            'user_id' => $user_id
                        ],
                        'aig-image-generation'
                    );

                    if ($existing_action) {
                        wp_send_json_success([
                            'message' => __('This post is already scheduled for image generation.', 'auto-image-generator-pro'),
                            'background' => true,
                            'count' => $count
                        ]);
                        return;
                    }
                }

                if (wp_next_scheduled('aig_background_generate_images', [$post_id, $count, $featured_only, $user_id])) {
                    wp_send_json_success([
                        'message' => __('This post is already scheduled for image generation.', 'auto-image-generator-pro'),
                        'background' => true,
                        'count' => $count
                    ]);
                    return;
                }

                wp_send_json_error(__('Failed to schedule the background image generation task.', 'auto-image-generator-pro'), 500);
            }
            return; // Important: exit after scheduling
        }

        // Process immediately for smaller requests
        try {
            error_log("[AIG Pro AJAX] Calling generate_images_for_post with post_id={$post_id}, count={$count}, featured_only=" . ($featured_only ? 'true' : 'false'));

            $result = $this->generate_images_for_post($post, $count, $featured_only);
            $generated_count = count($result);

            error_log("[AIG Pro AJAX] generate_images_for_post returned {$generated_count} images");

            if ($generated_count === 0) {
                error_log("[AIG Pro AJAX] No images were generated - returning error");
                wp_send_json_error(__('No images were generated. Please check API key validity, API limits, server error logs, and ensure the post content provides suitable prompts.', 'auto-image-generator-pro'), 500);
                return;
            }

            error_log("[AIG Pro AJAX] Sending success response with {$generated_count} images");
            wp_send_json_success([
                'message' => sprintf(_n('%d image generated and added successfully.', '%d images generated and added successfully.', $generated_count, 'auto-image-generator-pro'), $generated_count),
                'count' => $generated_count,
                'background' => false,
                'details' => $result // Include details for potential future use
            ]);

        } catch (\Throwable $t) {
            error_log('[AIG Pro Error] Immediate Generation Failed: ' . $t->getMessage() . ' Trace: ' . $t->getTraceAsString());
            wp_send_json_error($t->getMessage(), 500);
        }
        } catch (\Throwable $t) {
            error_log("[AIG Pro AJAX CRITICAL ERROR] Uncaught exception at top level: " . $t->getMessage() . " in " . $t->getFile() . " on line " . $t->getLine() . "\nTrace: " . $t->getTraceAsString());
            wp_send_json_error("Critical error: " . $t->getMessage(), 500);
        }
    }

    /**
     * Background task (WP Cron callback) for generating images.
     */
    public function background_generate_images(int $post_id, int $count, bool $featured_only, int $user_id): void {
        // Set user context for permissions and notifications
        wp_set_current_user($user_id);

        // Reload options in background context (can sometimes be necessary)
        $this->load_options();

        $post = get_post($post_id);
        $supported_types = apply_filters('aig_supported_post_types', ['post']);
        if (!$post || !in_array($post->post_type, $supported_types, true)) {
            $error_msg = sprintf(__('Background processing failed: Invalid post ID %d or unsupported type.', 'auto-image-generator-pro'), $post_id);
            error_log("[AIG Background Task Error] {$error_msg}");
            $this->add_completion_notification($user_id, $post_id, 0, $error_msg);
            return;
        }

        if (empty($this->api_key)) {
             $error_msg = __('Background processing failed: API key is not configured.', 'auto-image-generator-pro');
             error_log("[AIG Background Task Error] API key missing for user {$user_id}, post {$post_id}");
             $this->add_completion_notification($user_id, $post_id, 0, $error_msg);
            return;
        }

        $post_title_safe = esc_html(get_the_title($post_id) ?: '#' . $post_id);

        try {
            $result = $this->generate_images_for_post($post, $count, $featured_only);
            $generated_count = count($result);

             $message = $generated_count > 0
                ? sprintf(_n('Successfully generated %1$d image for post: %2$s', 'Successfully generated %1$d images for post: %2$s', $generated_count, 'auto-image-generator-pro'), $generated_count, $post_title_safe)
                : sprintf(__('Image generation completed, but 0 images were added for post: %s. Check logs for details.', 'auto-image-generator-pro'), $post_title_safe);

             $this->add_completion_notification($user_id, $post_id, $generated_count, $message);
             error_log("[AIG Background Task] Completed for post {$post_id}. Generated: {$generated_count}");

        } catch (\Throwable $t) {
            $error_message = sprintf(__('Background generation failed for post %1$s: %2$s', 'auto-image-generator-pro'), $post_title_safe, $t->getMessage());
            error_log('[AIG Background Task Error] For post ' . $post_id . ': ' . $t->getMessage() . ' Trace: ' . $t->getTraceAsString());
            $this->add_completion_notification($user_id, $post_id, 0, $error_message);
        }
    }

    /**
     * Action Scheduler callback for processing a post.
     * This is the entry point for Action Scheduler.
     *
     * @param int $post_id The post ID to process
     * @param int $count Number of images to generate
     * @param bool $featured_only Whether to only generate featured image
     * @param int $user_id User who initiated the request (for notifications)
     */
    public function process_post_action_scheduler(int $post_id, int $count = 1, bool $featured_only = false, int $user_id = 0): void {
        error_log("[AIG Pro] Processing post {$post_id} with Action Scheduler. Count: {$count}, Featured Only: " . ($featured_only ? 'Yes' : 'No') . ", User: {$user_id}");

        // Set user context for permissions and notifications
        if ($user_id > 0) {
            wp_set_current_user($user_id);
        }

        // Reload options in background context
        $this->load_options();

        $post = get_post($post_id);
        $supported_types = apply_filters('aig_supported_post_types', ['post']);
        if (!$post || !in_array($post->post_type, $supported_types, true)) {
            $error_msg = sprintf(__('Action Scheduler processing failed: Invalid post ID %d or unsupported type.', 'auto-image-generator-pro'), $post_id);
            error_log("[AIG Action Scheduler Error] {$error_msg}");
            $this->add_completion_notification($user_id, $post_id, 0, $error_msg);
            return;
        }

        if (empty($this->api_key)) {
             $error_msg = __('Action Scheduler processing failed: API key is not configured.', 'auto-image-generator-pro');
             error_log("[AIG Action Scheduler Error] API key missing for user {$user_id}, post {$post_id}");
             $this->add_completion_notification($user_id, $post_id, 0, $error_msg);
            return;
        }

        $post_title_safe = esc_html(get_the_title($post_id) ?: '#' . $post_id);

        try {
            // Set a longer time limit for Action Scheduler tasks
            @set_time_limit(apply_filters('aig_action_scheduler_time_limit', 300)); // 5 minutes, adjustable via filter

            $result = $this->generate_images_for_post($post, $count, $featured_only);
            $generated_count = count($result);

             $message = $generated_count > 0
                ? sprintf(_n('Successfully generated %1$d image for post: %2$s', 'Successfully generated %1$d images for post: %2$s', $generated_count, 'auto-image-generator-pro'), $generated_count, $post_title_safe)
                : sprintf(__('Image generation completed, but 0 images were added for post: %s. Check logs for details.', 'auto-image-generator-pro'), $post_title_safe);

             $this->add_completion_notification($user_id, $post_id, $generated_count, $message);
             error_log("[AIG Action Scheduler] Completed for post {$post_id}. Generated: {$generated_count}");

        } catch (\Throwable $t) {
            $error_message = sprintf(__('Action Scheduler processing failed for post %1$s: %2$s', 'auto-image-generator-pro'), $post_title_safe, $t->getMessage());
            error_log('[AIG Action Scheduler Error] For post ' . $post_id . ': ' . $t->getMessage() . ' Trace: ' . $t->getTraceAsString());
            $this->add_completion_notification($user_id, $post_id, 0, $error_message);
        }
    }

    /**
     * Schedule a post for background processing using Action Scheduler if available,
     * or fallback to WP-Cron.
     *
     * @param int $post_id The post ID to process
     * @param int $count Number of images to generate
     * @param bool $featured_only Whether to only generate featured image
     * @param int $user_id User who initiated the request (for notifications)
     * @return bool Whether the task was successfully scheduled
     */
    public function schedule_post_processing(int $post_id, int $count = 1, bool $featured_only = false, int $user_id = 0): bool {
        // Check if Action Scheduler is available
        if (function_exists('as_schedule_single_action') && function_exists('as_next_scheduled_action')) {
            // Check if this post is already scheduled
            $existing_action = as_next_scheduled_action(
                'aig_process_post_action_scheduler',
                [
                    'post_id' => $post_id,
                    'count' => $count,
                    'featured_only' => $featured_only,
                    'user_id' => $user_id
                ],
                'aig-image-generation'
            );

            if ($existing_action) {
                error_log("[AIG Pro] Post {$post_id} is already scheduled for processing with Action Scheduler (action ID: {$existing_action})");
                return false;
            }

            // Schedule with Action Scheduler
            $action_id = as_schedule_single_action(
                time(), // Run as soon as possible
                'aig_process_post_action_scheduler',
                [
                    'post_id' => $post_id,
                    'count' => $count,
                    'featured_only' => $featured_only,
                    'user_id' => $user_id
                ],
                'aig-image-generation'
            );

            if ($action_id) {
                error_log("[AIG Pro] Scheduled post {$post_id} for processing with Action Scheduler (action ID: {$action_id})");
                return true;
            }
        }

        // Fallback to WP-Cron
        if (!wp_next_scheduled('aig_background_generate_images', [$post_id, $count, $featured_only, $user_id])) {
            $scheduled = wp_schedule_single_event(
                time(),
                'aig_background_generate_images',
                [$post_id, $count, $featured_only, $user_id]
            );

            if ($scheduled) {
                error_log("[AIG Pro] Scheduled post {$post_id} for processing with WP-Cron");
                return true;
            }
        } else {
            error_log("[AIG Pro] Post {$post_id} is already scheduled for processing with WP-Cron");
            return false;
        }

        error_log("[AIG Pro] Failed to schedule post {$post_id} for processing");
        return false;
    }



    /**
     * Core function to generate images for a given post.
     * Orchestrates prompt creation, API calls, image saving, and content insertion.
     * Uses the specified `generate_image_with_gemini` method.
     *
     * @param \WP_Post $post The post object.
     * @param int $count The number of images requested.
     * @param bool $featured_only Whether to only generate a featured image.
     * @return array List of generated image details (url, prompt, context, etc.).
     * @throws \Exception If API key is missing or critical errors occur.
     */
    public function generate_images_for_post(\WP_Post $post, int $count, bool $featured_only): array {
        try {
            error_log("[AIG Pro] Starting generate_images_for_post for post ID: {$post->ID}, count: {$count}, featured_only: " . ($featured_only ? 'true' : 'false'));

            if (empty($this->api_key)) {
                error_log("[AIG Pro ERROR] API key is not configured in settings");
                throw new \Exception(__('Gemini API key is not configured in settings.', 'auto-image-generator-pro'));
            }

            $generated_images = [];
            $post_id = $post->ID;
            $post_title = $post->post_title;
            $post_content = $post->post_content;
            $count_remaining = min($this->max_images, max(1, $count)); // Ensure count is valid
            $cache_key_post = AIG_PRO_POST_CACHE_PREFIX . $post_id; // Cache for *this post's* generated URLs/prompts for specific prompts
            $cached_post_data = get_transient($cache_key_post) ?: []; // Format: [ 'prompt_hash' => 'image_url' ]

            error_log("[AIG Pro] Post data loaded: title length: " . strlen($post_title) . ", content length: " . strlen($post_content) . ", count_remaining: {$count_remaining}");
        } catch (\Throwable $t) {
            error_log("[AIG Pro CRITICAL ERROR] Exception in generate_images_for_post initialization: " . $t->getMessage() . " in " . $t->getFile() . " on line " . $t->getLine());
            return [];
        }

        // --- 1. Generate Featured Image ---
        try {
            if ($count_remaining > 0 && !has_post_thumbnail($post_id)) {
                error_log("[AIG Pro] Starting featured image generation for post {$post_id}");

                $this->current_keyword = $this->extract_primary_keyword($post_title);
                error_log("[AIG Pro] Extracted primary keyword: {$this->current_keyword}");

                // Get surrounding content for context
                $surrounding_content_for_featured = $post_title . " " . wp_strip_all_tags(wp_trim_words($post_content, 100));
                error_log("[AIG Pro] Created surrounding content for context, length: " . strlen($surrounding_content_for_featured));

                // Generate scene description using Gemini
                error_log("[AIG Pro] Calling generate_scene_description_with_gemini");
                $scene_desc = $this->generate_scene_description_with_gemini($this->current_keyword, $surrounding_content_for_featured, $post_title);
                error_log("[AIG Pro] Scene description generated, length: " . strlen($scene_desc));

                // Create prompt with scene description if available
                error_log("[AIG Pro] Creating featured image prompt");
                $prompt = $this->create_featured_image_prompt($this->current_keyword, $post_title, $scene_desc);
                $prompt_hash = md5($prompt);
                error_log("[AIG Pro] Created prompt, length: " . strlen($prompt) . ", hash: {$prompt_hash}");

                $image_url = $cached_post_data[$prompt_hash] ?? null; // Check post-specific cache
                $cache_hit = ($image_url !== null);
                $attachment_id = $image_url ? attachment_url_to_postid($image_url) : null;
                error_log("[AIG Pro] Cache check: hit=" . ($cache_hit ? 'Yes' : 'No') . ", attachment_id=" . ($attachment_id ?: 'null'));

                if (!$attachment_id) { // If not cached or attachment deleted, generate
                    error_log("[AIG Pro] No cached image found, generating new image with Gemini");
                    $image_data = $this->generate_image_with_gemini($prompt); // <<< USES THE SPECIFIED WORKING API CALL
                    if ($image_data) {
                        error_log("[AIG Pro] Image data received from Gemini, saving to media library");
                        $image_url = $this->save_image_to_media($image_data, $this->current_keyword, $post_id);
                        if ($image_url) {
                            error_log("[AIG Pro] Image saved to media library: {$image_url}");
                            $attachment_id = attachment_url_to_postid($image_url);
                            if ($attachment_id) {
                                error_log("[AIG Pro] Got attachment ID: {$attachment_id}, caching URL");
                                $cached_post_data[$prompt_hash] = $image_url; // Cache successful generation URL
                                set_transient($cache_key_post, $cached_post_data, DAY_IN_SECONDS * 7); // Cache for 7 days
                            } else {
                                $image_url = null; // Failed to get ID from URL
                                error_log("[AIG Pro] Post {$post_id}: Failed to get attachment ID for newly saved featured image URL: " . $image_url);
                            }
                        } else {
                            error_log("[AIG Pro] Post {$post_id}: Failed to save featured image data to media library for prompt: " . $prompt);
                        }
                    } else {
                        error_log("[AIG Pro] Post {$post_id}: Failed to generate featured image via API for prompt: " . $prompt);
                    }
                }

                if ($attachment_id) {
                    error_log("[AIG Pro] Setting post thumbnail with attachment ID: {$attachment_id}");
                    set_post_thumbnail($post_id, $attachment_id);
                    update_post_meta($attachment_id, '_wp_attachment_image_alt', sanitize_text_field($this->current_keyword)); // Ensure alt text is set/updated

                    $generated_images[] = [
                        'url' => $image_url, 'prompt' => $prompt, 'context' => $this->current_keyword,
                        'is_featured' => true, 'cache_hit' => $cache_hit, 'attachment_id' => $attachment_id,
                    ];
                    $count_remaining--;
                    error_log("[AIG Pro] Post {$post_id}: Set featured image (ID: {$attachment_id}, Cache Hit: " . ($cache_hit?'Yes':'No') . ")");
                } else {
                    error_log("[AIG Pro] Post {$post_id}: Could not set featured image (generation/saving failed or URL invalid).");
                }
            } else {
                error_log("[AIG Pro] Skipping featured image generation: count_remaining={$count_remaining}, has_thumbnail=" . (has_post_thumbnail($post_id) ? 'Yes' : 'No'));
            }
        } catch (\Throwable $t) {
            error_log("[AIG Pro CRITICAL ERROR] Exception in featured image generation: " . $t->getMessage() . " in " . $t->getFile() . " on line " . $t->getLine() . "\nTrace: " . $t->getTraceAsString());
        } // End Featured Image Generation

        // Exit if only featured image was requested or no more images needed
        if ($featured_only || $count_remaining <= 0) {
            return $generated_images;
        }

        // --- 2. Generate Content Images (Based on Headings First) ---
        try {
            error_log("[AIG Pro] Starting content image generation based on headings");

            preg_match_all('/<h([2-6])([^>]*)>(.*?)<\/h\1>/is', $post_content, $matches, PREG_SET_ORDER | PREG_OFFSET_CAPTURE);
            $headings = [];
            if (!empty($matches)) {
                error_log("[AIG Pro] Found " . count($matches) . " heading tags in content");
                foreach($matches as $match) {
                    $tag_full = $match[0][0]; // Full tag like <h2 class="foo">Bar</h2>
                    $text = strip_tags($match[3][0] ?? ''); // Content inside the tag
                    $offset = $match[0][1];
                    if(trim($text) !== '') {
                         $headings[] = ['tag_full' => $tag_full, 'text' => $text, 'offset' => $offset]; // Store full tag and text
                    }
                }
                error_log("[AIG Pro] Processed " . count($headings) . " valid headings with text");
            } else {
                error_log("[AIG Pro] No heading tags found in content");
            }

            $sections_without_media_indices = $this->find_sections_without_media_indices($post_content, $headings);
            error_log("[AIG Pro] Found " . count($sections_without_media_indices) . " heading sections without media");

            $added_to_heading_section = [];

            if (!empty($headings) && !empty($sections_without_media_indices)) {
                foreach ($sections_without_media_indices as $index) {
                    if ($count_remaining <= 0) {
                        error_log("[AIG Pro] No more images remaining to generate, breaking out of heading loop");
                        break;
                    }
                    if (!isset($headings[$index])) {
                        error_log("[AIG Pro] Heading index {$index} not found in headings array, skipping");
                        continue;
                    }

                    $heading = $headings[$index];
                    $context_text = $this->extract_primary_keyword($heading['text']);
                    $this->current_keyword = $context_text;
                    error_log("[AIG Pro] Processing heading: '{$heading['text']}', extracted keyword: '{$context_text}'");

                    // Get surrounding content for context - extract content around this heading
                    $heading_pos = $heading['offset'];
                    $next_heading_pos = isset($headings[$index + 1]) ? $headings[$index + 1]['offset'] : strlen($post_content);
                    $section_content = substr($post_content, $heading_pos, $next_heading_pos - $heading_pos);
                    $surrounding_content = $heading['text'] . " " . wp_strip_all_tags(wp_trim_words($section_content, 100));
                    error_log("[AIG Pro] Created surrounding content for context, length: " . strlen($surrounding_content));

                    // Generate scene description using Gemini
                    error_log("[AIG Pro] Generating scene description for heading context");
                    $scene_desc = $this->generate_scene_description_with_gemini($context_text, $surrounding_content, $post_title);
                    error_log("[AIG Pro] Scene description generated, length: " . strlen($scene_desc));

                    // Create prompt with scene description if available
                    error_log("[AIG Pro] Creating content image prompt");
                    $prompt = $this->create_content_image_prompt($context_text, $post_title, $scene_desc);
                    $prompt_hash = md5($prompt);
                    error_log("[AIG Pro] Created prompt, length: " . strlen($prompt) . ", hash: {$prompt_hash}");

                    $image_url = $cached_post_data[$prompt_hash] ?? null;
                    $cache_hit = ($image_url !== null);
                    $attachment_id = $image_url ? attachment_url_to_postid($image_url) : null;
                    error_log("[AIG Pro] Cache check: hit=" . ($cache_hit ? 'Yes' : 'No') . ", attachment_id=" . ($attachment_id ?: 'null'));

                    if(!$attachment_id) {
                        error_log("[AIG Pro] No cached image found, generating new image with Gemini");
                        $image_data = $this->generate_image_with_gemini($prompt); // <<< USES THE SPECIFIED WORKING API CALL
                        if ($image_data) {
                            error_log("[AIG Pro] Image data received from Gemini, saving to media library");
                            $image_url = $this->save_image_to_media($image_data, $this->current_keyword, $post_id);
                             if ($image_url) {
                                 error_log("[AIG Pro] Image saved to media library: {$image_url}");
                                 $attachment_id = attachment_url_to_postid($image_url);
                                 if ($attachment_id) {
                                     error_log("[AIG Pro] Got attachment ID: {$attachment_id}, caching URL");
                                     $cached_post_data[$prompt_hash] = $image_url;
                                     set_transient($cache_key_post, $cached_post_data, DAY_IN_SECONDS * 7);
                                 } else {
                                    $image_url = null;
                                    error_log("[AIG Pro] Post {$post_id}: Failed to get attachment ID for newly saved content image (heading context): " . $image_url);
                                 }
                             } else {
                                 error_log("[AIG Pro] Post {$post_id}: Failed to save content image (heading context) to media library for prompt: " . $prompt);
                             }
                        } else {
                            error_log("[AIG Pro] Post {$post_id}: Failed to generate content image (heading context) via API for prompt: " . $prompt);
                        }
                    }

                    if ($attachment_id) {
                        error_log("[AIG Pro] Setting alt text and adding to generated images array");
                        update_post_meta($attachment_id, '_wp_attachment_image_alt', sanitize_text_field($this->current_keyword));
                        $generated_images[] = [
                            'url' => $image_url, 'prompt' => $prompt, 'context' => $context_text,
                            'is_featured' => false, 'cache_hit' => $cache_hit, 'attachment_id' => $attachment_id,
                            'heading_index' => $index, 'heading_offset' => $heading['offset'], 'heading_tag' => $heading['tag_full'], // Pass full tag
                        ];
                        $added_to_heading_section[] = $index;
                        $count_remaining--;
                        error_log("[AIG Pro] Post {$post_id}: Generated/found content image for heading '{$context_text}' (ID: {$attachment_id}, Cache Hit: " . ($cache_hit?'Yes':'No') . ")");
                    } else {
                        error_log("[AIG Pro] Post {$post_id}: Could not generate/save content image for heading '{$context_text}'.");
                    }
                } // End loop through sections without media
                error_log("[AIG Pro] Completed heading-based image generation. Added " . count($added_to_heading_section) . " images");
            } else {
                error_log("[AIG Pro] Skipping heading-based image generation: headings=" . count($headings) . ", sections_without_media=" . count($sections_without_media_indices));
            } // End Heading Image Generation
        } catch (\Throwable $t) {
            error_log("[AIG Pro CRITICAL ERROR] Exception in content image generation (headings): " . $t->getMessage() . " in " . $t->getFile() . " on line " . $t->getLine() . "\nTrace: " . $t->getTraceAsString());
        }

        // --- 3. Generate Remaining Images (Diverse Contexts) ---
        try {
            if ($count_remaining > 0) {
                error_log("[AIG Pro] Starting diverse context image generation, remaining count: {$count_remaining}");

                $diverse_contexts = $this->get_diverse_contexts($post, $count_remaining + count($generated_images)); // Get enough potential contexts
                $used_contexts = array_column($generated_images, 'context'); // Track used contexts to avoid duplicates

                error_log("[AIG Pro] Found " . count($diverse_contexts) . " diverse contexts, " . count($used_contexts) . " already used");

                foreach ($diverse_contexts as $context) {
                    if ($count_remaining <= 0) {
                        error_log("[AIG Pro] No more images remaining to generate, breaking out of diverse contexts loop");
                        break;
                    }

                    $context_keyword = $this->extract_primary_keyword($context);
                    if (in_array($context_keyword, $used_contexts)) {
                        error_log("[AIG Pro] Skipping already used context keyword: '{$context_keyword}'");
                        continue; // Skip if keyword already used
                    }

                    error_log("[AIG Pro] Processing diverse context: '{$context}', extracted keyword: '{$context_keyword}'");
                    $this->current_keyword = $context_keyword;

                    error_log("[AIG Pro] Creating content image prompt");
                    $prompt = $this->create_content_image_prompt($context_keyword, $post_title);
                    $prompt_hash = md5($prompt);
                    error_log("[AIG Pro] Created prompt, length: " . strlen($prompt) . ", hash: {$prompt_hash}");

                    $image_url = $cached_post_data[$prompt_hash] ?? null;
                    $cache_hit = ($image_url !== null);
                    $attachment_id = $image_url ? attachment_url_to_postid($image_url) : null;
                    error_log("[AIG Pro] Cache check: hit=" . ($cache_hit ? 'Yes' : 'No') . ", attachment_id=" . ($attachment_id ?: 'null'));

                    if(!$attachment_id) {
                        error_log("[AIG Pro] No cached image found, generating new image with Gemini");
                        $image_data = $this->generate_image_with_gemini($prompt); // <<< USES THE SPECIFIED WORKING API CALL
                        if ($image_data) {
                            error_log("[AIG Pro] Image data received from Gemini, saving to media library");
                            $image_url = $this->save_image_to_media($image_data, $this->current_keyword, $post_id);
                            if ($image_url) {
                                error_log("[AIG Pro] Image saved to media library: {$image_url}");
                                $attachment_id = attachment_url_to_postid($image_url);
                                if ($attachment_id) {
                                    error_log("[AIG Pro] Got attachment ID: {$attachment_id}, caching URL");
                                    $cached_post_data[$prompt_hash] = $image_url;
                                    set_transient($cache_key_post, $cached_post_data, DAY_IN_SECONDS * 7);
                                } else {
                                    $image_url = null;
                                    error_log("[AIG Pro] Post {$post_id}: Failed to get attachment ID for newly saved content image (diverse context): " . $image_url);
                                }
                            } else {
                                error_log("[AIG Pro] Post {$post_id}: Failed to save content image (diverse context) to media library for prompt: " . $prompt);
                            }
                        } else {
                            error_log("[AIG Pro] Post {$post_id}: Failed to generate content image (diverse context) via API for prompt: " . $prompt);
                        }
                    }

                    if ($attachment_id) {
                        error_log("[AIG Pro] Setting alt text and adding to generated images array");
                        update_post_meta($attachment_id, '_wp_attachment_image_alt', sanitize_text_field($this->current_keyword));
                        $generated_images[] = [
                            'url' => $image_url, 'prompt' => $prompt, 'context' => $context_keyword,
                            'is_featured' => false, 'cache_hit' => $cache_hit, 'attachment_id' => $attachment_id,
                            'heading_index' => null, // No specific heading association
                        ];
                        $used_contexts[] = $context_keyword; // Add to used list
                        $count_remaining--;
                        error_log("[AIG Pro] Post {$post_id}: Generated/found content image for diverse context '{$context_keyword}' (ID: {$attachment_id}, Cache Hit: " . ($cache_hit?'Yes':'No') . ")");
                    } else {
                        error_log("[AIG Pro] Post {$post_id}: Could not generate/save content image for diverse context '{$context_keyword}'.");
                    }
                } // End loop through diverse contexts
                error_log("[AIG Pro] Completed diverse context image generation");
            } else {
                error_log("[AIG Pro] Skipping diverse context image generation, no images remaining to generate");
            } // End Diverse Context Image Generation
        } catch (\Throwable $t) {
            error_log("[AIG Pro CRITICAL ERROR] Exception in diverse context image generation: " . $t->getMessage() . " in " . $t->getFile() . " on line " . $t->getLine() . "\nTrace: " . $t->getTraceAsString());
        }

        // --- 4. Add Content Images to Post ---
        try {
            error_log("[AIG Pro] Starting content image insertion process");

            $content_images_to_insert = array_filter($generated_images, fn($img) => !$img['is_featured'] && isset($img['attachment_id']));
            error_log("[AIG Pro] Found " . count($content_images_to_insert) . " content images to insert");

            if (!empty($content_images_to_insert)) {
                error_log("[AIG Pro] Calling add_images_to_content with " . count($content_images_to_insert) . " images");
                $this->add_images_to_content($post_id, $post_content, $content_images_to_insert, $headings);
            } else {
                error_log("[AIG Pro] Post {$post_id}: No content images were generated or valid for insertion.");
            }
        } catch (\Throwable $t) {
            error_log("[AIG Pro CRITICAL ERROR] Exception in content image insertion: " . $t->getMessage() . " in " . $t->getFile() . " on line " . $t->getLine() . "\nTrace: " . $t->getTraceAsString());
        }

        try {
            error_log("[AIG Pro] Cleaning post cache and returning results");
            clean_post_cache($post_id); // Clear post cache after potential updates
            return $generated_images;
        } catch (\Throwable $t) {
            error_log("[AIG Pro CRITICAL ERROR] Exception in final cleanup: " . $t->getMessage() . " in " . $t->getFile() . " on line " . $t->getLine() . "\nTrace: " . $t->getTraceAsString());
            return $generated_images; // Still return the generated images even if cache cleaning fails
        }
    }



    /**
     * **CRITICAL: Uses the exact working Gemini API function structure.**
     * Generates images with Gemini API using the confirmed working model, endpoint, and request structure.
     * Includes enhanced rate limiting, caching (using transients), retry logic, and error logging around the core API call.
     *
     * @param string $prompt The generation prompt.
     * @return array|false An array ['data' => base64_string, 'mime_type' => string] or false on failure.
     */
    private function generate_image_with_gemini(string $prompt): array|false {
        try {
            error_log("[AIG Pro Gemini API] Starting generate_image_with_gemini with prompt: " . substr($prompt, 0, 100) . "...");

            $cache_key = AIG_PRO_GEMINI_CACHE_PREFIX . md5($prompt); // Use defined, versioned prefix
            $cached_result = get_transient($cache_key);

            if ($cached_result) {
                // Basic validation of cached data structure
                if (is_array($cached_result) && isset($cached_result['data']) && isset($cached_result['mime_type']) && !empty($cached_result['data'])) {
                    error_log("[AIG Pro Gemini API] Cache HIT for prompt hash: " . md5($prompt));
                    return $cached_result;
                } else {
                    error_log("[AIG Pro Gemini API] Deleting INVALID cache entry for prompt hash: " . md5($prompt));
                    delete_transient($cache_key); // Delete invalid or empty cache entry
                }
            }

            error_log("[AIG Pro Gemini API] Cache MISS for prompt hash: " . md5($prompt));

            // --- Rate Limiting (Microtime-based) ---
            $current_microtime = microtime(true);
            $time_since_last = $current_microtime - $this->api_last_request_time;

            // Per-minute limit reset
            if ($this->api_request_count >= $this->api_request_limit && $time_since_last < 60.0) {
                $sleep_time = 60.0 - $time_since_last;
                error_log("[AIG Pro Rate Limit] Minute limit ({$this->api_request_limit}) reached. Sleeping for " . round($sleep_time, 2) . " seconds.");
                usleep((int) ($sleep_time * 1000000));
                $this->api_request_count = 0;
                $this->api_last_request_time = microtime(true); // Reset time after sleep
            } elseif ($time_since_last >= 60.0) {
                // Reset count if more than a minute has passed since the *very first* request in the current batch
                $this->api_request_count = 0;
                // Don't reset api_last_request_time here, reset it *after* the current request
            }

            // Inter-request cooldown
            if ($this->api_request_count > 0 && $this->api_cooldown > 0 && $time_since_last < $this->api_cooldown) {
                $cooldown_sleep = $this->api_cooldown - $time_since_last;
                usleep((int) ($cooldown_sleep * 1000000));
            }

            // --- API Call Setup (EXACT working structure) ---
            // DO NOT CHANGE $model or $api_url structure unless user explicitly asks again.
            $model = apply_filters('aig_gemini_model', 'gemini-2.0-flash-exp'); // Using the exact working model name
            $api_url = "https://generativelanguage.googleapis.com/v1beta/models/{$model}:generateContent?key=" . urlencode($this->api_key);

            // DO NOT CHANGE $request_data structure (contents, generationConfig, responseModalities)
            $request_data = [
                'contents' => [
                    [
                        'role' => 'user',
                        'parts' => [
                            ['text' => $prompt],
                        ],
                    ],
                ],
                'generationConfig' => [
                    'temperature' => apply_filters('aig_gemini_temperature', 0.4),
                    'topK' => apply_filters('aig_gemini_topk', 32),
                    'topP' => apply_filters('aig_gemini_topp', 1),
                    'maxOutputTokens' => apply_filters('aig_gemini_max_tokens', 2048),
                    'responseModalities' => ['Text', 'Image'], // Using the exact working format with capital first letters
                ],
                // Include safety settings (can be adjusted via filter if needed)
                'safetySettings' => apply_filters('aig_gemini_safety_settings', [
                    ['category' => 'HARM_CATEGORY_HARASSMENT', 'threshold' => 'BLOCK_MEDIUM_AND_ABOVE'],
                    ['category' => 'HARM_CATEGORY_HATE_SPEECH', 'threshold' => 'BLOCK_MEDIUM_AND_ABOVE'],
                    ['category' => 'HARM_CATEGORY_SEXUALLY_EXPLICIT', 'threshold' => 'BLOCK_MEDIUM_AND_ABOVE'],
                    ['category' => 'HARM_CATEGORY_DANGEROUS_CONTENT', 'threshold' => 'BLOCK_MEDIUM_AND_ABOVE'],
                ]),
            ];

            $args = [
                'method' => 'POST',
                'headers' => [
                    'Content-Type' => 'application/json',
                    'Accept' => 'application/json', // Good practice
                    'User-Agent' => 'WordPress/AIG_Pro/' . AIG_PRO_VERSION, // Identify the client
                ],
                'body' => wp_json_encode($request_data),
                'timeout' => AIG_PRO_API_TIMEOUT,
                'redirection' => 0, // Don't follow redirects
                'httpversion' => '1.1', // Specify HTTP version
            ];

            // Update request count and timestamp *before* the request
            $this->api_request_count++;
            $this->api_last_request_time = microtime(true);

            error_log("[AIG Pro Gemini API] Sending request ({$this->api_request_count}/{$this->api_request_limit}). URL: {$api_url}. Prompt (start): " . substr($prompt, 0, 100) . "...");
            error_log("[AIG Pro Gemini API Debug] Request data: " . json_encode($request_data));

            try {
                error_log("[AIG Pro Gemini API] Calling wp_remote_post...");

                // Debug the request
                error_log("[AIG Pro Gemini API Debug] Request URL: {$api_url}");
                error_log("[AIG Pro Gemini API Debug] Request headers: " . json_encode($args['headers']));
                error_log("[AIG Pro Gemini API Debug] Request body length: " . strlen($args['body']));

                // Make the API request
                $response = wp_remote_post($api_url, $args);

                error_log("[AIG Pro Gemini API] wp_remote_post call completed");

                // Debug the response type
                if (is_array($response)) {
                    error_log("[AIG Pro Gemini API Debug] Response is an array with " . count($response) . " elements");
                    if (isset($response['headers'])) {
                        error_log("[AIG Pro Gemini API Debug] Response headers: " . json_encode($response['headers']));
                    }
                } elseif (is_wp_error($response)) {
                    error_log("[AIG Pro Gemini API Debug] Response is a WP_Error object");
                } else {
                    error_log("[AIG Pro Gemini API Debug] Response is of type: " . gettype($response));
                }
            } catch (\Throwable $t) {
                error_log("[AIG Pro Gemini API CRITICAL ERROR] Exception in wp_remote_post call: " . $t->getMessage() . " in " . $t->getFile() . " on line " . $t->getLine() . "\nTrace: " . $t->getTraceAsString());
                throw $t; // Re-throw to be caught by the outer try-catch
            }

            // --- Error Handling & Retry ---
            try {
                error_log("[AIG Pro Gemini API] Processing response for error handling...");
                $response_code = wp_remote_retrieve_response_code($response);
                $response_body = wp_remote_retrieve_body($response);
                $response_message = wp_remote_retrieve_response_message($response);

                error_log("[AIG Pro Gemini API] Response code: {$response_code}, message: {$response_message}, body length: " . strlen($response_body));

                // Check for WP_Error first (network issues, DNS, timeouts etc.)
                if (is_wp_error($response)) {
                    $error_message = $response->get_error_message();
                    error_log("[AIG Pro Gemini API Error] WP_Error (Attempt 1): {$error_message}. URL: {$api_url}. Prompt: {$prompt}");

                    try {
                        // Retry ONCE for WP_Error
                        error_log("[AIG Pro Gemini API Retry] Retrying after WP_Error. Delay: " . AIG_PRO_API_RETRY_DELAY . "s");
                        sleep(AIG_PRO_API_RETRY_DELAY);
                        $this->api_request_count++; // Increment for retry attempt
                        $this->api_last_request_time = microtime(true); // Update time for retry

                        error_log("[AIG Pro Gemini API Retry] Sending retry request...");
                        $response = wp_remote_post($api_url, $args);
                        error_log("[AIG Pro Gemini API Retry] Retry request completed");

                        if (is_wp_error($response)) {
                            $error_message_retry = $response->get_error_message();
                            error_log("[AIG Pro Gemini API Error] WP_Error (Attempt 2): {$error_message_retry}. Aborting.");
                            return false; // Failed after retry
                        }

                        // If retry succeeded, update response vars
                        $response_code = wp_remote_retrieve_response_code($response);
                        $response_body = wp_remote_retrieve_body($response);
                        $response_message = wp_remote_retrieve_response_message($response);
                        error_log("[AIG Pro Gemini API Retry] Retry successful. Code: {$response_code}, message: {$response_message}");
                    } catch (\Throwable $t) {
                        error_log("[AIG Pro Gemini API CRITICAL ERROR] Exception during retry after WP_Error: " . $t->getMessage() . " in " . $t->getFile() . " on line " . $t->getLine());
                        return false; // Failed during retry
                    }
                }
            } catch (\Throwable $t) {
                error_log("[AIG Pro Gemini API CRITICAL ERROR] Exception in error handling: " . $t->getMessage() . " in " . $t->getFile() . " on line " . $t->getLine() . "\nTrace: " . $t->getTraceAsString());
                return false;
            }

            // Check for HTTP status codes indicating potential retry (5xx, 429)
            try {
                error_log("[AIG Pro Gemini API] Checking for retryable HTTP errors...");
                $is_retryable_http_error = $response_code >= 500 || $response_code === 429;

                if ($is_retryable_http_error) {
                    error_log("[AIG Pro Gemini API Error] HTTP Status {$response_code} {$response_message} (Attempt 1). URL: {$api_url}. Prompt: {$prompt}. Body: " . ($response_code != 429 ? $response_body : '(Rate limited response body hidden)'));

                    try {
                        // Retry ONCE for these HTTP errors
                        error_log("[AIG Pro Gemini API Retry] Retrying after HTTP {$response_code}. Delay: " . AIG_PRO_API_RETRY_DELAY . "s");
                        sleep(AIG_PRO_API_RETRY_DELAY);
                        $this->api_request_count++; // Increment for retry attempt
                        $this->api_last_request_time = microtime(true); // Update time for retry

                        error_log("[AIG Pro Gemini API Retry] Sending retry request...");
                        $response = wp_remote_post($api_url, $args);
                        error_log("[AIG Pro Gemini API Retry] Retry request completed");

                        // Re-check response after retry
                        $response_code = wp_remote_retrieve_response_code($response);
                        $response_body = wp_remote_retrieve_body($response);
                        $response_message = wp_remote_retrieve_response_message($response);
                        error_log("[AIG Pro Gemini API Retry] Retry response: code={$response_code}, message={$response_message}, body length=" . strlen($response_body));

                        if (is_wp_error($response) || $response_code >= 400) { // Check for WP_Error or ANY error (>=400) on retry
                            $error_message_retry = is_wp_error($response) ? $response->get_error_message() : "HTTP Status {$response_code} {$response_message}";
                            error_log("[AIG Pro Gemini API Error] Failed on Retry (Attempt 2): {$error_message_retry}. Body: {$response_body}");
                            return false; // Failed after retry
                        }
                        error_log("[AIG Pro Gemini API Retry] Retry successful. Code: {$response_code}");
                    } catch (\Throwable $t) {
                        error_log("[AIG Pro Gemini API CRITICAL ERROR] Exception during HTTP error retry: " . $t->getMessage() . " in " . $t->getFile() . " on line " . $t->getLine());
                        return false; // Failed during retry
                    }
                }
            } catch (\Throwable $t) {
                error_log("[AIG Pro Gemini API CRITICAL ERROR] Exception in HTTP error handling: " . $t->getMessage() . " in " . $t->getFile() . " on line " . $t->getLine() . "\nTrace: " . $t->getTraceAsString());
                return false;
            }

            // Handle other non-successful HTTP codes (4xx client errors, unexpected codes)
            try {
                if ($response_code !== 200) {
                    error_log("[AIG Pro Gemini API Error] Unsuccessful HTTP Status: {$response_code} {$response_message}. URL: {$api_url}. Prompt: {$prompt}. Body length: " . strlen($response_body));

                    try {
                        // Try to decode potential error JSON from API
                        $data = json_decode($response_body, true, 512, JSON_THROW_ON_ERROR);
                        if (isset($data['error']['message'])) {
                            error_log("[AIG Pro Gemini API Error Detail] API Message: " . $data['error']['message']);
                        }
                    } catch (\Throwable $t) {
                        error_log("[AIG Pro Gemini API Warning] Failed to decode error response JSON: " . $t->getMessage());
                        // Fallback to standard json_decode without throwing
                        $data = json_decode($response_body, true);
                        if (json_last_error() === JSON_ERROR_NONE && isset($data['error']['message'])) {
                            error_log("[AIG Pro Gemini API Error Detail] API Message (fallback decode): " . $data['error']['message']);
                        } else {
                            // Log a portion of the raw response if JSON parsing fails
                            error_log("[AIG Pro Gemini API Error Detail] Raw response (first 500 chars): " . substr($response_body, 0, 500));
                        }
                    }

                    return false;
                }
            } catch (\Throwable $t) {
                error_log("[AIG Pro Gemini API CRITICAL ERROR] Exception in non-200 HTTP code handling: " . $t->getMessage() . " in " . $t->getFile() . " on line " . $t->getLine() . "\nTrace: " . $t->getTraceAsString());
                return false;
            }

            // --- Process SUCCESSFUL (200 OK) Response ---
            try {
                error_log("[AIG Pro Gemini API] Received Successful Response (200 OK). Processing body...");

                // Save the response to a file for debugging
                try {
                    $debug_dir = sys_get_temp_dir() . '/aig_debug';
                    if (!file_exists($debug_dir)) {
                        mkdir($debug_dir, 0755, true);
                    }
                    $debug_file = $debug_dir . '/gemini_response_' . time() . '.json';
                    file_put_contents($debug_file, $response_body);
                    error_log("[AIG Pro Gemini API Debug] Saved full response to file: {$debug_file}");
                } catch (\Throwable $save_error) {
                    error_log("[AIG Pro Gemini API Warning] Failed to save debug file: " . $save_error->getMessage());
                }

                error_log("[AIG Pro Gemini API Debug] Response body (first 500 chars): " . substr($response_body, 0, 500) . "...");

                // Check response size
                $response_size = strlen($response_body);
                error_log("[AIG Pro Gemini API Debug] Response size: {$response_size} bytes");

                // Check for common response patterns
                if (strpos($response_body, '"candidates"') !== false) {
                    error_log("[AIG Pro Gemini API Debug] Response contains 'candidates' field");
                }
                if (strpos($response_body, '"content"') !== false) {
                    error_log("[AIG Pro Gemini API Debug] Response contains 'content' field");
                }
                if (strpos($response_body, '"parts"') !== false) {
                    error_log("[AIG Pro Gemini API Debug] Response contains 'parts' field");
                }
                if (strpos($response_body, '"inlineData"') !== false) {
                    error_log("[AIG Pro Gemini API Debug] Response contains 'inlineData' field");
                }
            } catch (\Throwable $t) {
                error_log("[AIG Pro Gemini API CRITICAL ERROR] Exception while logging successful response: " . $t->getMessage() . " in " . $t->getFile() . " on line " . $t->getLine());
                // Continue despite logging error
            }

            try {
                error_log("[AIG Pro Gemini API] Attempting to decode JSON response...");

                // Check if response body is empty or not valid JSON
                if (empty($response_body)) {
                    error_log("[AIG Pro Gemini API CRITICAL ERROR] Response body is empty");
                    return false;
                }

                // Log the first part of the response for debugging
                error_log("[AIG Pro Gemini API Debug] Response body start: " . substr($response_body, 0, 200));

                // Check for common error patterns in the response
                if (strpos($response_body, '<!DOCTYPE html>') !== false || strpos($response_body, '<html') !== false) {
                    error_log("[AIG Pro Gemini API CRITICAL ERROR] Response contains HTML instead of JSON. This may indicate a server error or proxy issue.");
                    // Log more details about the HTML response
                    if (preg_match('/<title>(.*?)<\/title>/is', $response_body, $matches)) {
                        error_log("[AIG Pro Gemini API CRITICAL ERROR] HTML page title: " . $matches[1]);
                    }
                    // Try to extract error message from HTML
                    if (preg_match('/<body[^>]*>(.*?)<\/body>/is', $response_body, $matches)) {
                        $body_content = strip_tags($matches[1]);
                        $body_content = preg_replace('/\s+/', ' ', $body_content);
                        error_log("[AIG Pro Gemini API CRITICAL ERROR] HTML body content: " . substr($body_content, 0, 500));
                    }
                    return false;
                }

                // Check for invalid JSON characters
                $invalid_chars = [];
                for ($i = 0; $i < min(strlen($response_body), 1000); $i++) {
                    $char = ord($response_body[$i]);
                    // Check for control characters (except allowed ones like tab, newline)
                    if (($char < 32 && !in_array($char, [9, 10, 13])) || $char > 127) {
                        $invalid_chars[] = "Position $i: Char code $char";
                        if (count($invalid_chars) >= 10) break; // Limit to 10 invalid chars
                    }
                }

                if (!empty($invalid_chars)) {
                    error_log("[AIG Pro Gemini API CRITICAL ERROR] Found invalid JSON characters: " . implode(', ', $invalid_chars));
                    // Try to clean the response
                    $cleaned_response = preg_replace('/[\x00-\x08\x0B\x0C\x0E-\x1F\x80-\xFF]/', '', $response_body);
                    error_log("[AIG Pro Gemini API] Attempting to use cleaned response");
                    $response_body = $cleaned_response;
                }

                // Try to decode with error handling
                $data = json_decode($response_body, true, 512, JSON_THROW_ON_ERROR);
                error_log("[AIG Pro Gemini API] JSON successfully decoded");
            } catch (\JsonException $je) {
                error_log("[AIG Pro Gemini API CRITICAL ERROR] JSON decode error: " . $je->getMessage() . " in " . $je->getFile() . " on line " . $je->getLine());
                error_log("[AIG Pro Gemini API CRITICAL ERROR] Response body (first 1000 chars): " . substr($response_body, 0, 1000));

                // Try to identify the specific JSON syntax error
                $error_msg = $je->getMessage();
                $error_position = null;
                if (preg_match('/at position (\d+)/', $error_msg, $matches)) {
                    $error_position = (int)$matches[1];
                    $start = max(0, $error_position - 20);
                    $length = 40;
                    $context = substr($response_body, $start, $length);
                    error_log("[AIG Pro Gemini API CRITICAL ERROR] JSON error context around position $error_position: " . $context);
                }

                // Try a fallback decode without throwing
                $data = json_decode($response_body, true);
                if (json_last_error() !== JSON_ERROR_NONE) {
                    error_log("[AIG Pro Gemini API Error] Fallback JSON decode also failed. Error: " . json_last_error_msg());

                    // Try one more approach - remove any BOM or other problematic characters
                    $cleaned_response = preg_replace('/^[\x00-\x1F\x80-\xFF]+/', '', $response_body);
                    $data = json_decode($cleaned_response, true);
                    if (json_last_error() !== JSON_ERROR_NONE) {
                        error_log("[AIG Pro Gemini API Error] All JSON decode attempts failed");
                        return false;
                    }
                    error_log("[AIG Pro Gemini API] JSON decode succeeded after removing BOM/control chars");
                } else {
                    error_log("[AIG Pro Gemini API] Fallback JSON decode succeeded despite exception");
                }
            }

            // Log the structure of the response for debugging
            try {
                error_log("[AIG Pro Gemini API Debug] Attempting to encode response structure for logging...");
                $structure_json = json_encode(array_keys($data), JSON_THROW_ON_ERROR);
                error_log("[AIG Pro Gemini API Debug] Response structure: " . $structure_json);

                if (isset($data['candidates']) && is_array($data['candidates']) && !empty($data['candidates'])) {
                    error_log("[AIG Pro Gemini API Debug] Found " . count($data['candidates']) . " candidates");
                    if (isset($data['candidates'][0]['content']) && isset($data['candidates'][0]['content']['parts'])) {
                        error_log("[AIG Pro Gemini API Debug] Found " . count($data['candidates'][0]['content']['parts']) . " parts in first candidate");
                    }
                }
            } catch (\Throwable $t) {
                error_log("[AIG Pro Gemini API CRITICAL ERROR] Exception in JSON encoding response structure: " . $t->getMessage());
                error_log("[AIG Pro Gemini API CRITICAL ERROR] Will continue processing despite logging error");
                // Continue processing despite logging error
            }

            // --- Extract image data using the EXACT structure from the user's working code ---
            try {
                error_log("[AIG Pro Gemini API] Attempting to extract image data from response...");

                // Check candidates -> content -> parts -> inlineData
                if (!empty($data['candidates'][0]['content']['parts'])) {
                    error_log("[AIG Pro Gemini API Debug] Found candidates and parts in response with " . count($data['candidates'][0]['content']['parts']) . " parts");

                    foreach ($data['candidates'][0]['content']['parts'] as $index => $part) {
                        error_log("[AIG Pro Gemini API Debug] Examining part " . ($index + 1));

                        if (isset($part['inlineData']['data']) && isset($part['inlineData']['mimeType'])) {
                            error_log("[AIG Pro Gemini API Debug] Found inlineData with mimeType: " . $part['inlineData']['mimeType']);

                            // Check if mimeType starts with 'image/' (PHP 7.4 compatible)
                            if (strpos($part['inlineData']['mimeType'], 'image/') === 0) {
                                $result = [
                                    'data' => $part['inlineData']['data'], // Base64 encoded image data
                                    'mime_type' => $part['inlineData']['mimeType']
                                ];

                                // Basic check for empty data string
                                if (empty($result['data'])) {
                                    error_log("[AIG Pro Gemini API Warning] Found image part but 'inlineData.data' is empty. Prompt: {$prompt}.");
                                    continue; // Check next part if any
                                }

                                $data_length = strlen($result['data']);
                                error_log("[AIG Pro Gemini API Success] Extracted image data (Mime: {$result['mime_type']}, Size: {$data_length} bytes). Prompt hash: " . md5($prompt));

                                try {
                                    // Cache the successful result using transient
                                    error_log("[AIG Pro Gemini API] Caching successful result with key: {$cache_key}");
                                    set_transient($cache_key, $result, HOUR_IN_SECONDS * 6); // Cache for 6 hours
                                } catch (\Throwable $t) {
                                    error_log("[AIG Pro Gemini API Warning] Failed to cache result: " . $t->getMessage());
                                    // Continue despite caching failure
                                }

                                return $result; // Return the first valid image found
                            } else {
                                error_log("[AIG Pro Gemini API Debug] Part has inlineData but not an image MIME type: " . $part['inlineData']['mimeType']);
                            }
                        } else {
                            error_log("[AIG Pro Gemini API Debug] Part " . ($index + 1) . " does not contain valid inlineData structure");
                        }
                    }

                    // If loop finishes without finding a valid image part
                    error_log("[AIG Pro Gemini API Warning] Response received (200 OK), but no suitable 'inlineData' with image MIME type and data found in 'parts'. Prompt: {$prompt}");
                    try {
                        error_log("[AIG Pro Gemini API Debug] Response structure: " . wp_json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES));
                    } catch (\Throwable $t) {
                        error_log("[AIG Pro Gemini API Warning] Failed to encode response structure for logging: " . $t->getMessage());
                    }
                    return false;
                } else {
                    error_log("[AIG Pro Gemini API CRITICAL ERROR] No valid content parts found in response");
                    return false;
                }
            } catch (\Throwable $t) {
                error_log("[AIG Pro Gemini API CRITICAL ERROR] Exception while extracting image data: " . $t->getMessage() . " in " . $t->getFile() . " on line " . $t->getLine() . "\nTrace: " . $t->getTraceAsString());
                return false;
            }

            // Check for API error in response
            if (isset($data['error'])) {
                try {
                    // Log specific API error structure if returned even with 200 OK (unlikely but possible)
                    $api_error_code = $data['error']['code'] ?? 'N/A';
                    $api_error_message = $data['error']['message'] ?? 'No message provided.';
                    error_log("[AIG Pro Gemini API Error] API returned error structure within 200 OK response. Code: {$api_error_code}. Message: {$api_error_message}. Prompt: {$prompt}");
                } catch (\Throwable $t) {
                    error_log("[AIG Pro Gemini API CRITICAL ERROR] Exception while processing API error: " . $t->getMessage());
                }
                return false;
            } else {
                try {
                    // If response format is completely unexpected (e.g., no candidates, no error)
                    error_log("[AIG Pro Gemini API Error] Unexpected response structure (200 OK but no candidates/error/parts). Prompt: {$prompt}");
                    error_log("[AIG Pro Gemini API Debug] Response: " . wp_json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES));
                } catch (\Throwable $t) {
                    error_log("[AIG Pro Gemini API CRITICAL ERROR] Exception while logging unexpected response: " . $t->getMessage());
                }
                return false;
            }
        } catch (\Throwable $t) {
            error_log("[AIG Pro Gemini API CRITICAL ERROR] Exception in API call: " . $t->getMessage() . " in " . $t->getFile() . " on line " . $t->getLine() . "\nTrace: " . $t->getTraceAsString());

            // Try to get more information about the error
            $error_type = get_class($t);
            $error_code = $t->getCode();
            error_log("[AIG Pro Gemini API CRITICAL ERROR] Error type: {$error_type}, Error code: {$error_code}");

            // Check for specific error types
            if ($t instanceof \JsonException) {
                error_log("[AIG Pro Gemini API CRITICAL ERROR] JSON error detected. This may indicate malformed JSON in the request or response.");
            } elseif ($t instanceof \TypeError) {
                error_log("[AIG Pro Gemini API CRITICAL ERROR] Type error detected. This may indicate incorrect data types being passed to functions.");
            }

            return false;
        }
    }

    /**
     * Call the Gemini API for text-only responses (used for alt text and captions)
     *
     * @param string $prompt The prompt to send to the API
     * @return string|false The text response or false on failure
     */
    private function call_gemini_api_text(string $prompt): string|false {
        if (empty($this->api_key)) {
            error_log("[AIG Pro] API key is not set. Cannot call Gemini API.");
            return false;
        }

        // Check for cached response
        $cache_key = 'aig_gemini_text_' . md5($prompt);
        $cached_result = get_transient($cache_key);
        if ($cached_result !== false) {
            error_log("[AIG Pro] Using cached text response for prompt hash: " . md5($prompt));
            return $cached_result;
        }

        // Rate limiting
        $current_microtime = microtime(true);
        $time_since_last = $current_microtime - $this->api_last_request_time;

        // Per-minute limit reset
        if ($this->api_request_count >= $this->api_request_limit && $time_since_last < 60.0) {
            $sleep_time = 60.0 - $time_since_last;
            error_log("[AIG Pro Rate Limit] Minute limit ({$this->api_request_limit}) reached. Sleeping for " . round($sleep_time, 2) . " seconds.");
            usleep((int) ($sleep_time * 1000000));
            $this->api_request_count = 0;
            $this->api_last_request_time = microtime(true); // Reset time after sleep
        } elseif ($time_since_last >= 60.0) {
            // Reset count if more than a minute has passed
            $this->api_request_count = 0;
        }

        // Inter-request cooldown
        if ($this->api_request_count > 0 && $this->api_cooldown > 0 && $time_since_last < $this->api_cooldown) {
            $cooldown_sleep = $this->api_cooldown - $time_since_last;
            usleep((int) ($cooldown_sleep * 1000000));
        }

        // API Call Setup
        $model = apply_filters('aig_gemini_text_model', 'gemini-1.5-flash');
        $api_url = "https://generativelanguage.googleapis.com/v1beta/models/{$model}:generateContent?key=" . urlencode($this->api_key);

        $request_data = [
            'contents' => [
                [
                    'role' => 'user',
                    'parts' => [
                        ['text' => $prompt],
                    ],
                ],
            ],
            'generationConfig' => [
                'temperature' => apply_filters('aig_gemini_temperature', 0.2),
                'topK' => apply_filters('aig_gemini_topk', 32),
                'topP' => apply_filters('aig_gemini_topp', 1),
                'maxOutputTokens' => apply_filters('aig_gemini_max_tokens', 1024),
            ],
            'safetySettings' => apply_filters('aig_gemini_safety_settings', [
                ['category' => 'HARM_CATEGORY_HARASSMENT', 'threshold' => 'BLOCK_MEDIUM_AND_ABOVE'],
                ['category' => 'HARM_CATEGORY_HATE_SPEECH', 'threshold' => 'BLOCK_MEDIUM_AND_ABOVE'],
                ['category' => 'HARM_CATEGORY_SEXUALLY_EXPLICIT', 'threshold' => 'BLOCK_MEDIUM_AND_ABOVE'],
                ['category' => 'HARM_CATEGORY_DANGEROUS_CONTENT', 'threshold' => 'BLOCK_MEDIUM_AND_ABOVE'],
            ]),
        ];

        $args = [
            'method' => 'POST',
            'headers' => [
                'Content-Type' => 'application/json',
                'Accept' => 'application/json',
                'User-Agent' => 'WordPress/AIG_Pro/' . AIG_PRO_VERSION,
            ],
            'body' => wp_json_encode($request_data),
            'timeout' => AIG_PRO_API_TIMEOUT,
            'redirection' => 0,
            'httpversion' => '1.1',
        ];

        // Update request count and timestamp
        $this->api_request_count++;
        $this->api_last_request_time = microtime(true);

        error_log("[AIG Pro Gemini Text API] Sending request ({$this->api_request_count}/{$this->api_request_limit}). Prompt (start): " . substr($prompt, 0, 100) . "...");

        try {
            // Make the API request
            $response = wp_remote_post($api_url, $args);

            // Check for errors
            if (is_wp_error($response)) {
                error_log("[AIG Pro Gemini Text API Error] " . $response->get_error_message());
                return false;
            }

            $response_code = wp_remote_retrieve_response_code($response);
            $response_body = wp_remote_retrieve_body($response);

            if ($response_code !== 200) {
                error_log("[AIG Pro Gemini Text API Error] HTTP Status: {$response_code}. Response: " . substr($response_body, 0, 500));
                return false;
            }

            // Parse the response
            $data = json_decode($response_body, true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                error_log("[AIG Pro Gemini Text API Error] JSON decode error: " . json_last_error_msg());
                error_log("[AIG Pro Gemini Text API Error] Response body: " . substr($response_body, 0, 500));
                return false;
            }

            // Extract the text from the response
            if (isset($data['candidates'][0]['content']['parts'][0]['text'])) {
                $text = $data['candidates'][0]['content']['parts'][0]['text'];

                // Cache the result
                set_transient($cache_key, $text, HOUR_IN_SECONDS * 6); // Cache for 6 hours

                return $text;
            } else {
                error_log("[AIG Pro Gemini Text API Error] Unexpected response structure: " . substr($response_body, 0, 500));
                return false;
            }
        } catch (\Throwable $t) {
            error_log("[AIG Pro Gemini Text API CRITICAL ERROR] " . $t->getMessage() . " in " . $t->getFile() . " on line " . $t->getLine());
            return false;
        }
    }

    /**
     * Finds indices of heading sections (H2-H6) that likely don't contain media elements.
     * Used to determine good spots for inserting content images.
     *
     * @param string $content The post content.
     * @param array $headings Array of heading information (tag_full, text, offset).
     * @return array Indices of headings in the input array that likely lack media.
     */
    private function find_sections_without_media_indices(string $content, array $headings): array {
        $indices_without_media = [];
        if (empty($headings)) {
            return $indices_without_media;
        }

        // Find positions of common media tags and embeds
        preg_match_all('/<img|<video|<picture|<figure class="wp-block-image|<figure class="wp-block-video|<div class="wp-block-embed|<iframe/i', $content, $media_matches, PREG_OFFSET_CAPTURE);
        $media_positions = empty($media_matches[0]) ? [] : array_column($media_matches[0], 1);
        sort($media_positions);

        $content_len = strlen($content);

        for ($i = 0; $i < count($headings); $i++) {
            // Section starts *after* the current heading tag
            $section_start = $headings[$i]['offset'] + strlen($headings[$i]['tag_full']);
            // Section ends *before* the next heading tag, or at the end of the content
            $section_end = isset($headings[$i + 1]) ? $headings[$i + 1]['offset'] : $content_len;

            $has_media_in_section = false;
            foreach ($media_positions as $media_pos) {
                 if ($media_pos >= $section_start && $media_pos < $section_end) {
                    $has_media_in_section = true;
                    break; // Found media in this section
                }
                 // Optimization: If media position is beyond section end, no need to check further media positions for this section
                 if ($media_pos >= $section_end) {
                     break;
                 }
            }

            if (!$has_media_in_section) {
                $indices_without_media[] = $i; // Add index of the heading
            }
        }

        return $indices_without_media;
    }

    /**
     * Add generated images (non-featured) to the post content intelligently.
     * Prioritizes inserting images after headings that lack media.
     * Inserts remaining images distributed between paragraphs.
     * Uses a safer method of reconstructing content rather than direct replacement.
     *
     * @param int $post_id The ID of the post to update.
     * @param string $original_content The original post content.
     * @param array $images_to_add Array of image details for content images.
     * @param array $headings Array of heading details (used for placement).
     */
    private function add_images_to_content(int $post_id, string $original_content, array $images_to_add, array $headings): void {
        if (empty($images_to_add)) {
            error_log("[AIG Pro] Post {$post_id}: No valid content images provided to add_images_to_content.");
            return;
        }

        // Ensure all images have a valid attachment ID
        $valid_images = array_filter($images_to_add, fn($img) => !empty($img['attachment_id']) && $img['attachment_id'] > 0);
        if (empty($valid_images)) {
             error_log("[AIG Pro] Post {$post_id}: No content images with valid attachment IDs found for insertion.");
            return;
        }

        // Sort images: Place heading-associated images first, ordered by heading offset.
        usort($valid_images, function($a, $b) {
            $a_has_heading = isset($a['heading_index']) && $a['heading_index'] !== null;
            $b_has_heading = isset($b['heading_index']) && $b['heading_index'] !== null;

            if ($a_has_heading && !$b_has_heading) return -1; // a (heading) comes first
            if (!$a_has_heading && $b_has_heading) return 1;  // b (heading) comes first
            if ($a_has_heading && $b_has_heading) {
                // Both have headings, sort by offset
                return ($a['heading_offset'] ?? 0) <=> ($b['heading_offset'] ?? 0);
            }
            return 0; // Keep relative order if neither has heading (e.g., diverse context images)
        });

        $new_content = '';       // Build the new content progressively
        $last_pos = 0;           // Track the position processed in the original content
        $images_placed_count = 0;
        $images_not_placed_under_headings = [];

        // --- 1. Place images associated with specific headings ---
        $heading_indices_used = []; // Track which heading tags we've inserted after
        foreach ($valid_images as $image) {
            if (isset($image['heading_index']) && $image['heading_index'] !== null && isset($image['heading_tag'], $image['heading_offset'])) {
                 $heading_tag = $image['heading_tag']; // The full e.g. <h2>...</h2> tag
                 $heading_offset = $image['heading_offset'];
                 $heading_index = $image['heading_index']; // Index in the $headings array

                 // Check if we haven't already placed an image after this *exact* heading tag instance
                 if (!in_array($heading_index, $heading_indices_used, true)) {
                     // Find the precise position of this heading tag starting from the expected offset
                     // Use a small tolerance in case of minor whitespace changes? No, stick to exact match for now.
                     $pos_in_original = strpos($original_content, $heading_tag, $heading_offset);

                     if ($pos_in_original === $heading_offset) { // Ensure it's the correct instance
                         // Append content segment before this heading
                         $new_content .= substr($original_content, $last_pos, $pos_in_original - $last_pos);
                         // Append the heading itself
                         $new_content .= $heading_tag;
                         // Append the image block HTML right after the heading
                         $new_content .= "\n\n" . $this->create_image_block_html($image) . "\n\n";

                         // Update last position processed in original content to be *after* this heading tag
                         $last_pos = $pos_in_original + strlen($heading_tag);

                         $images_placed_count++;
                         $heading_indices_used[] = $heading_index; // Mark this heading index as used
                         error_log("[AIG Pro] Post {$post_id}: Placed image ID {$image['attachment_id']} after heading index {$heading_index} at offset {$heading_offset}.");
                         continue; // Move to the next image
                     } else {
                         // Couldn't find heading tag exactly at the expected position. Log and save for later placement.
                         error_log("[AIG Pro] Post {$post_id}: Could not precisely locate heading tag '{$heading_tag}' at offset {$heading_offset} for image placement. Expected: {$heading_offset}, Found: " . ($pos_in_original === false ? 'Not Found' : $pos_in_original));
                         $images_not_placed_under_headings[] = $image;
                     }
                 } else {
                    // Heading index already used, save image for general placement
                    error_log("[AIG Pro] Post {$post_id}: Heading index {$heading_index} already used for placement. Skipping image ID {$image['attachment_id']} for specific heading placement.");
                    $images_not_placed_under_headings[] = $image;
                 }
            } else {
                 // Image not associated with a heading, save for general placement
                 $images_not_placed_under_headings[] = $image;
            }
        }

        // Append the remaining part of the original content after the last processed heading
        $new_content .= substr($original_content, $last_pos);

        // --- 2. Place remaining images (not tied to specific headings or failed heading placement) ---
        if (!empty($images_not_placed_under_headings)) {
            error_log("[AIG Pro] Post {$post_id}: Attempting to place " . count($images_not_placed_under_headings) . " remaining images between paragraphs.");
            // Find suitable insertion points (after paragraphs) in the *currently constructed* new_content
            preg_match_all('/<\/p>\s*/i', $new_content, $potential_points, PREG_OFFSET_CAPTURE | PREG_PATTERN_ORDER);

            $insert_points = [];
            if (!empty($potential_points[0])) {
                 foreach($potential_points[0] as $point) {
                     $pos = $point[1] + strlen($point[0]); // Position *after* the matched </p> and any trailing space
                     $insert_points[] = $pos;
                 }
                 // Remove duplicates and sort positions
                 $insert_points = array_unique($insert_points);
                 sort($insert_points);
            }

            $num_remaining = count($images_not_placed_under_headings);
            $num_points = count($insert_points);
            $images_to_insert_between_p = [];

            if ($num_points > 0) {
                // Distribute images somewhat evenly across available insertion points
                $step = max(1, floor($num_points / ($num_remaining + 1))); // Calculate step size
                $points_to_use = [];

                for ($i = 0; $i < $num_remaining; $i++) {
                    $point_index = min(($i + 1) * $step - 1, $num_points - 1); // Get index of point to use
                     if (isset($insert_points[$point_index])) {
                         // Associate image with this insertion point
                         $images_to_insert_between_p[$insert_points[$point_index]] = $images_not_placed_under_headings[$i];
                     } else {
                         // Fallback: If calculation is off, just use the last point (should be rare)
                         if ($i === $num_remaining -1 && $num_points > 0) {
                            $images_to_insert_between_p[$insert_points[$num_points-1]] = $images_not_placed_under_headings[$i];
                         }
                         error_log("[AIG Pro] Post {$post_id}: Could not map image {$i} to an insertion point (Index: {$point_index}, Total Points: {$num_points}).");
                         // Consider adding remaining images at the end as a further fallback if this happens often
                     }
                }

                 // Sort insertion points in reverse order to avoid offset issues during insertion
                 krsort($images_to_insert_between_p);

                 // Insert images into the new_content string
                 foreach ($images_to_insert_between_p as $pos => $image) {
                     $img_html_with_spacing = "\n\n" . $this->create_image_block_html($image) . "\n\n";
                     $new_content = substr_replace($new_content, $img_html_with_spacing, $pos, 0);
                     $images_placed_count++;
                     error_log("[AIG Pro] Post {$post_id}: Placed remaining image ID {$image['attachment_id']} between paragraphs at offset {$pos}.");
                 }

            } else {
                 // Fallback: No paragraph tags found. Prepend remaining images to the content.
                 error_log("[AIG Pro] Post {$post_id}: No paragraph tags found for general image placement. Prepending remaining images.");
                 $prepend_html = '';
                 foreach (array_reverse($images_not_placed_under_headings) as $image) { // Reverse to maintain intended order when prepending
                     $prepend_html .= "\n\n" . $this->create_image_block_html($image) . "\n\n";
                 }
                 if (!empty($prepend_html)) {
                     $new_content = $prepend_html . $new_content;
                     $images_placed_count += count($images_not_placed_under_headings);
                 }
            }
        } // End placing remaining images

        // --- 3. Update Post Content ---
        // Only update if content has actually changed and we placed at least one image
        if ($images_placed_count > 0 && $new_content !== $original_content) {
            $update_result = wp_update_post([
                'ID' => $post_id,
                'post_content' => $new_content // Use the reconstructed content
            ], true); // Pass true to return WP_Error on failure

            if (is_wp_error($update_result)) {
                 error_log("[AIG Pro] Post {$post_id}: Error updating post content: " . $update_result->get_error_message());
            } else {
                 error_log("[AIG Pro] Post {$post_id}: Successfully updated post content with {$images_placed_count} images.");
                 clean_post_cache($post_id); // Clear cache after successful update
            }
        } elseif ($images_placed_count > 0) {
             error_log("[AIG Pro] Post {$post_id}: Content processed for image insertion, but no effective change detected compared to original content.");
        } else {
             error_log("[AIG Pro] Post {$post_id}: No images were successfully placed into the content.");
        }
    }

    /**
     * Helper to create WordPress block editor compatible image HTML (wp:image block).
     *
     * @param array $image Image details including attachment_id, url, context.
     * @return string HTML string for the image block.
     */
    private function create_image_block_html(array $image): string {
         if (!isset($image['attachment_id'], $image['url'], $image['context'])) return '';

         $attachment_id = (int) $image['attachment_id'];
         if ($attachment_id <= 0) return ''; // Must have a valid ID

         $alt_text = esc_attr(sanitize_text_field($image['context'])); // Sanitize context for alt
         $image_meta = wp_get_attachment_metadata($attachment_id);
         $size = 'large'; // Default size to insert
         $image_src_data = wp_get_attachment_image_src($attachment_id, $size);

         // Fallback if 'large' size doesn't exist
         if (!$image_src_data) {
             $size = 'full';
             $image_src_data = wp_get_attachment_image_src($attachment_id, $size);
         }

         // If still no data, something is wrong with the attachment
         if (!$image_src_data) {
             error_log("[AIG Pro] Could not get image src data for attachment ID: {$attachment_id}");
             return ''; // Cannot create block without src
         }

         $img_url = esc_url($image_src_data[0]);
         $img_width = $image_src_data[1] ?? ($image_meta['width'] ?? '');
         $img_height = $image_src_data[2] ?? ($image_meta['height'] ?? '');
         $size_class = 'size-' . $size;
         $img_class = "wp-image-{$attachment_id}";

         // Construct the wp:image block structure as a string
         // Note: Direct HTML insertion is simpler than block serialization here.
         // This creates a standard <figure> structure that the block editor recognizes.
         return sprintf(
            '<figure class="wp-block-image %1$s"><img src="%2$s" alt="%3$s" class="%4$s" width="%5$s" height="%6$s"/></figure>',
            esc_attr($size_class),
            $img_url,
            $alt_text,
            esc_attr($img_class),
            esc_attr($img_width),
            esc_attr($img_height)
            // Consider adding figcaption if desired: sprintf('<figcaption class="wp-element-caption">%s</figcaption>', esc_html($image['context']))
         );
    }

    // --- Utility Functions (Mostly unchanged, minor cleanups) ---

    /** Extracts a primary keyword/topic from text (title, heading). */
    private function extract_primary_keyword(string $text): string {
        $text = trim(strtolower(wp_strip_all_tags($text)));
        if (empty($text)) return 'image'; // Default fallback

        // Patterns to capture core subjects (refined)
        $patterns = [
            '/^how\s+to\s+([^?:\-]+)/i', // How to [keyword]
            '/^(?:the\s+)?(?:ultimate|complete|essential|beginner\'s)\s+guide\s+to\s+(.+)/i', // Guide to [keyword]
            '/^\d+\s+(?:best|top|essential|great|cool)\s*(?:ways|tips|tricks|methods|steps|strategies|ideas|examples|plugins|tools|themes|reasons)\s+(?:to|for|why)\s+(.+)/i', // Listicle: X ways to [keyword]
            '/^(?:best|top|essential)\s+([^?:\-]+)(\s+for|\s+in|$)/i', // Best [keyword] for...
            '/^what\s+(?:is|are)\s+(?:an?|the)\s+([^?]+)/i', // What is [keyword]?
            '/^understanding\s+([^?:\-]+)/i', // Understanding [keyword]
            '/^exploring\s+([^?:\-]+)/i', // Exploring [keyword]
            '/^review(?:ing)?\s+of\s+(.+)/i', // Review of [keyword]
            '/^(.*?):\s+/i', // Keyword: Description
        ];
        foreach ($patterns as $pattern) {
            if (preg_match($pattern, $text, $matches) && isset($matches[1]) && trim($matches[1])) {
                $keyword = trim($matches[1]);
                // Avoid overly generic matches like 'how to' itself
                if (str_word_count($keyword) >= 1 && strlen($keyword) > 3) return $this->clean_keyword($keyword);
            }
        }

        // Fallback: Remove common stop words and take first few significant words
         $stop_words = [' a ', ' an ', ' the ', ' and ', ' or ', ' but ', ' for ', ' nor ', ' on ', ' at ', ' to ', ' from ', ' by ', ' with ', ' in ', ' is ', ' are ', ' was ', ' were ', ' of ', ' i ', ' you ', ' he ', ' she ', ' it ', ' we ', ' they ', ' me ', ' him ', ' her ', ' us ', ' them ', ' my ', ' your ', ' his ', ' its ', ' our ', ' their ', ' how ', ' what ', ' when ', ' where ', ' why ', ' guide ', ' tips ', ' ways ', ' steps ', ' ultimate ', ' complete ', ' best ', ' top ', ' new ', ' using ', ' about ', ' introduction ', ' tutorial ', ' review ', ' learn ', ' make ', ' use ', ' create ', ' build ', ' get ', ' set ', ' up '];
         $filtered_text = str_ireplace($stop_words, ' ', ' ' . $text . ' ');
         $filtered_text = preg_replace('/\s+/', ' ', $filtered_text); // Normalize spaces
         $words = array_filter(explode(' ', trim($filtered_text)), fn($w) => strlen($w) > 2); // Filter short words

        if (empty($words)) {
             // If filtering removed everything, revert to original text's first few words
             $words = explode(' ', $text);
             return $this->clean_keyword(implode(' ', array_slice($words, 0, 4))); // Take up to 4 words
        }

        // Prioritize proper nouns (Capitalized words) if found
         if (preg_match_all('/([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*)/', wp_strip_all_tags($text), $proper_matches) && !empty($proper_matches[0])) {
              // Find the longest proper noun phrase
              usort($proper_matches[0], fn($a, $b) => strlen($b) <=> strlen($a));
              if(strlen($proper_matches[0][0]) > 4) return $this->clean_keyword($proper_matches[0][0]);
         }

        // Default to first few filtered words
        return $this->clean_keyword(implode(' ', array_slice($words, 0, 4))); // Take up to 4 significant words
    }

    /** Cleans a keyword string for use in prompts or filenames. */
    private function clean_keyword(string $keyword): string {
        $keyword = preg_replace('/[^\p{L}\p{N}\s\-]/u', '', $keyword); // Remove most punctuation except hyphens
        $keyword = preg_replace('/\s+/', ' ', trim($keyword)); // Normalize whitespace
        // Limit length
        if (str_word_count($keyword) > 5) {
             $keyword = implode(' ', array_slice(explode(' ', $keyword), 0, 5));
        }
        return $keyword ?: 'image'; // Ensure non-empty return
    }

    /** Gets diverse context strings from post title, categories, tags, excerpt, content keywords. */
    private function get_diverse_contexts(\WP_Post $post, int $needed): array {
        $contexts = []; $post_id = $post->ID;

        // 1. Post Title
        if ($post->post_title) $contexts[] = $post->post_title;

        // 2. Categories
        $categories = get_the_category($post_id);
        if (!empty($categories)) {
            foreach ($categories as $category) {
                if (count($contexts) >= $needed + 5) break; // Get a few extra to filter later
                $contexts[] = $category->name;
            }
        }

        // 3. Tags
        if (count($contexts) < $needed + 5) {
            $tags = get_the_tags($post_id);
            if (!empty($tags)) {
                foreach ($tags as $tag) {
                    if (count($contexts) >= $needed + 5) break;
                    $contexts[] = $tag->name;
                }
            }
        }

        // 4. Excerpt
        if (count($contexts) < $needed + 5 && !empty($post->post_excerpt)) {
            $contexts[] = $post->post_excerpt;
        }

        // 5. Content Keywords (if still needed)
        if (count($contexts) < $needed + 5) {
            $content = wp_strip_all_tags($post->post_content);
            $words = str_word_count(strtolower($content), 1);
            // More comprehensive stop word list (consider moving to a class property if reused often)
            $common_words = ['the','and','a','to','of','in','is','that','it','for','on','with','as','are','this','be','or','an','by','we','you','at','not','but','from','if','so','then','will','can','just','like','get','make','use','see','know','think','go','come','say','tell','ask','work','try','call','need','feel','become','leave','put','mean','keep','let','begin','seem','help','talk','turn','start','show','hear','play','run','move','live','believe','hold','bring','write','provide','sit','stand','lose','pay','meet','include','continue','set','learn','change','lead','understand','watch','follow','stop','create','speak','read','allow','add','spend','grow','open','walk','win','offer','remember','love','consider','appear','buy','wait','serve','die','send','expect','build','stay','fall','cut','reach','kill','raise','pass','sell','require','report','decide','pull'];
            $words = array_diff($words, $common_words);
            $word_counts = array_count_values($words);
            arsort($word_counts); // Sort by frequency

            foreach (array_keys($word_counts) as $keyword) {
                if (count($contexts) >= $needed + 10) break; // Get even more for filtering content words
                if (strlen($keyword) > 4) { // Only consider longer keywords from content
                     $contexts[] = $keyword;
                }
            }
        }

        // Process and filter contexts
        $contexts = array_map('trim', $contexts); // Trim whitespace
        $contexts = array_filter($contexts, fn($ctx) => strlen($ctx) > 3); // Remove very short contexts
        $contexts = array_map('strtolower', $contexts); // Lowercase for unique check
        $contexts = array_values(array_unique($contexts)); // Remove duplicates

        // Try to ensure variety - rudimentary check, could be improved
        $final_contexts = [];
        if(!empty($contexts)) {
            $final_contexts[] = $contexts[0]; // Always include the first (likely title)
            $word_base = explode(' ', $contexts[0]);
            for ($i=1; $i < count($contexts); $i++) {
                 if (count($final_contexts) >= $needed) break;
                 $word_current = explode(' ', $contexts[$i]);
                 // Simple check: add if it doesn't share too many words with the first context
                 if (count(array_intersect($word_base, $word_current)) < count($word_current) * 0.6) {
                     $final_contexts[] = $contexts[$i];
                 }
            }
        }


        // If still not enough, add variations of title (less ideal)
        while (count($final_contexts) < $needed && $post->post_title) {
             $variation_num = count($final_contexts) + 1 - (in_array(strtolower($post->post_title), $final_contexts) ? 1 : 0);
             if ($variation_num > 0) {
                $final_contexts[] = $post->post_title . ' - aspect ' . $variation_num;
             } else {
                 break; // Avoid infinite loop if title somehow causes issues
             }
        }

        return array_slice($final_contexts, 0, $needed); // Return exactly the number needed
    }

    /** Filters intermediate image sizes based on plugin setting. */
    public function filter_intermediate_image_sizes(array $sizes): array {
        if ($this->generate_all_sizes) {
             // If generating all, return the input array BUT potentially remove custom AIG sizes if they existed
             // unset($sizes['aig-webp-full']); // Example if we added custom sizes previously
            return $sizes;
        }

        // Keep only essential WP default sizes + any filtered essential sizes
        $essential_sizes = apply_filters('aig_essential_image_sizes', [
            'thumbnail',
            'medium',
            'medium_large', // Often used
            'large'
            // 'full' is implicitly handled by wp_generate_attachment_metadata
        ]);

        $filtered_sizes = [];
        foreach ($essential_sizes as $size_name) {
            if (isset($sizes[$size_name])) {
                $filtered_sizes[$size_name] = $sizes[$size_name];
            }
        }
        // Always keep the 'full' size reference implicitly handled by metadata generation
        return $filtered_sizes;
    }

    /**
     * Gets posts without featured image OR without any <img> tag in content.
     * Uses WP Cache for short-term caching.
     * Limited query for performance.
     */
    private function get_posts_without_images(int $limit = 25): array {
        $cache_key = AIG_PRO_CACHE_PREFIX . 'posts_no_images_' . $limit;
        $cached_posts = wp_cache_get($cache_key, 'aig_local'); // Use a local group
        if (false !== $cached_posts) {
            return $cached_posts;
        }

        global $wpdb;
        $supported_types_in = "'" . implode("','", array_map('esc_sql', apply_filters('aig_supported_post_types', ['post']))) . "'";

        // Query for posts that DON'T have a _thumbnail_id OR DON'T have an <img> tag in content
         $query = $wpdb->prepare(
             "SELECT p.ID, p.post_title, p.post_content FROM {$wpdb->posts} p
              WHERE p.post_type IN ({$supported_types_in})
              AND p.post_status = 'publish'
              AND ( NOT EXISTS (SELECT 1 FROM {$wpdb->postmeta} pm WHERE pm.post_id = p.ID AND pm.meta_key = '_thumbnail_id' AND pm.meta_value > 0)
                    OR p.post_content NOT LIKE %s )
              ORDER BY p.post_date DESC
              LIMIT %d",
             '%<img%', // Check for presence of <img> tag
             $limit
         );
        $posts = $wpdb->get_results($query);

        wp_cache_set($cache_key, $posts, 'aig_local', 300); // Cache for 5 minutes
        return $posts;
    }

    /** Helper to get PHP memory limit in bytes. */
    private function get_memory_limit(): int {
        $memory_limit = ini_get('memory_limit');
        if (empty($memory_limit) || $memory_limit === '-1') {
            // No limit or unlimited, return a sensible high value (e.g., 512MB)
            return 512 * 1024 * 1024;
        }
        $val = intval(preg_replace('/[^0-9]/', '', $memory_limit));
        $last = strtolower(substr($memory_limit, -1));
        switch ($last) {
            case 'g': $val *= 1024; // Intentional fall-through
            case 'm': $val *= 1024;
            case 'k': $val *= 1024;
        }
        // Ensure at least 128M, common minimum for WP operations
        return max($val, 128 * 1024 * 1024);
    }

    // --- Image Saving & Optimization (Refined GD handling) ---

    /**
     * Saves base64 image data to the WP Media Library.
     * Handles optimization, resizing, and WebP conversion based on settings.
     *
     * @param array $image_data ['data' => base64 string, 'mime_type' => string]
     * @param string $title Desired title/alt text base.
     * @param int $post_id Post ID to attach to (0 for unattached).
     * @return string|null URL of the saved image or null on failure.
     */
    private function save_image_to_media(array $image_data, string $title, int $post_id = 0): ?string {
        require_once(ABSPATH . 'wp-admin/includes/image.php');
        require_once(ABSPATH . 'wp-admin/includes/file.php');
        require_once(ABSPATH . 'wp-admin/includes/media.php');

        $decoded_image = base64_decode($image_data['data'], true); // Strict mode
        if ($decoded_image === false) {
            error_log('[AIG Pro] Failed to decode base64 image data.');
            return null;
        }

        $mime_type = $image_data['mime_type'];
        $ext = str_replace('image/', '', $mime_type);
        if ($ext === 'jpeg') $ext = 'jpg';
        $allowed_exts = apply_filters('aig_allowed_upload_extensions', ['jpg', 'png', 'gif', 'webp']);
        if (!in_array($ext, $allowed_exts, true)) {
            error_log('[AIG Pro] Invalid or disallowed image extension detected from API: ' . $ext);
            return null;
        }

        // Generate filename based on keyword/title
        $keyword_prefix = sanitize_title($this->current_keyword ?: $title);
        if (empty($keyword_prefix)) $keyword_prefix = 'aig-generated-image'; // Fallback filename
        $filename_base = sanitize_file_name($keyword_prefix);

        // Create temporary file to work with
        $temp_file_path = wp_tempnam($filename_base . '.' . $ext);
        if (!$temp_file_path) {
            error_log('[AIG Pro] Could not create temporary file.');
            unset($decoded_image); // Free memory
            return null;
        }

        $saved = file_put_contents($temp_file_path, $decoded_image);
        unset($decoded_image); // Free memory
        if (!$saved) {
            error_log('[AIG Pro] Failed to write image data to temporary file: ' . $temp_file_path);
            unlink($temp_file_path);
            return null;
        }

        // --- Process Image (Resize, Optimize, WebP) ---
        $webp_path = null;
        $optimization_performed = false;

        // 1. Resize if needed (before optimization)
        if ($this->image_optimization_enabled) {
            $max_dimension = apply_filters('aig_max_image_dimension', 2560);
            $this->resize_image_if_needed($temp_file_path, $max_dimension);
            clearstatcache(true, $temp_file_path); // Update file stat cache
        }

        // 2. Optimize Original (if enabled)
        if ($this->image_optimization_enabled) {
            if ($this->optimize_image($temp_file_path)) {
                $optimization_performed = true;
                clearstatcache(true, $temp_file_path); // Update file stat cache
            }
        }

        // 3. Convert to WebP (if enabled and supported)
        if ($this->use_webp && function_exists('imagewebp')) {
            $temp_webp_path = $this->convert_to_webp($temp_file_path);
            if ($temp_webp_path) {
                clearstatcache(true, $temp_webp_path);
                // Note: We handle moving the WebP file later, alongside the main file
            }
        }
        // --- End Image Processing ---


        // Prepare final upload destination
        $upload_dir_info = wp_upload_dir(); // Use wp_upload_dir for year/month structure
        if ($upload_dir_info['error']) {
            error_log('[AIG Pro] Error getting upload directory: ' . $upload_dir_info['error']);
             unlink($temp_file_path); if (isset($temp_webp_path) && file_exists($temp_webp_path)) unlink($temp_webp_path);
            return null;
        }

        $filename = wp_unique_filename($upload_dir_info['path'], $filename_base . '.' . $ext);
        $final_upload_path = $upload_dir_info['path'] . '/' . $filename;

        // Move the (potentially optimized/resized) main file
        if (!rename($temp_file_path, $final_upload_path)) { // Use rename (move) instead of copy+unlink
            error_log('[AIG Pro] Failed to move temporary file to final destination: ' . $final_upload_path);
            if(file_exists($temp_file_path)) unlink($temp_file_path); // Clean up temp if rename failed but file exists
            if (isset($temp_webp_path) && file_exists($temp_webp_path)) unlink($temp_webp_path); // Clean up temp webp
            return null;
        }

        // Move the WebP file if it was created
        $final_webp_path = null;
        if (isset($temp_webp_path) && file_exists($temp_webp_path)) {
            $webp_filename = pathinfo($temp_webp_path, PATHINFO_BASENAME);
            $final_webp_path_candidate = $upload_dir_info['path'] . '/' . $webp_filename;
            if (rename($temp_webp_path, $final_webp_path_candidate)) {
                $final_webp_path = $final_webp_path_candidate;
                 error_log("[AIG Pro] Moved WebP file to: " . $final_webp_path);
            } else {
                error_log('[AIG Pro] Failed to move temporary WebP file to final destination: ' . $final_webp_path_candidate);
                if(file_exists($temp_webp_path)) unlink($temp_webp_path); // Clean up temp webp
            }
        }

        // --- Create Attachment ---
        $filetype = wp_check_filetype(basename($final_upload_path), null);
        $attachment_data = [
            'guid' => $upload_dir_info['url'] . '/' . $filename,
            'post_mime_type' => $filetype['type'],
            'post_title' => sanitize_text_field($title),
            'post_content' => '', // Optional description
            'post_status' => 'inherit',
        ];

        $attach_id = wp_insert_attachment($attachment_data, $final_upload_path, $post_id);

        if (is_wp_error($attach_id)) {
            error_log('[AIG Pro] Error inserting attachment: ' . $attach_id->get_error_message());
            unlink($final_upload_path); // Clean up failed attachment file
            if ($final_webp_path && file_exists($final_webp_path)) unlink($final_webp_path); // Clean up WebP too
            return null;
        }

        // Generate attachment metadata (thumbnails, etc.)
        // This is where WP generates intermediate sizes based on filter_intermediate_image_sizes
        $attach_meta = wp_generate_attachment_metadata($attach_id, $final_upload_path);

        // Add WebP metadata if generated successfully
        if ($final_webp_path && file_exists($final_webp_path) && !empty($attach_meta)) {
             if(!isset($attach_meta['sizes'])) $attach_meta['sizes'] = []; // Ensure sizes array exists
             $webp_meta_info = $this->get_webp_metadata_info($final_webp_path, $upload_dir_info);
             if($webp_meta_info) {
                 // Store top-level WebP info (optional, but can be useful)
                 $attach_meta['aig_webp_info'] = $webp_meta_info;
                 // Add a 'full-webp' entry in sizes array for reference (some plugins might look for this)
                  if(isset($attach_meta['width'], $attach_meta['height'])) {
                    $attach_meta['sizes']['full-webp'] = [
                        'file' => $webp_meta_info['filename'],
                        'width' => $attach_meta['width'], // Assume same dimensions as original full
                        'height' => $attach_meta['height'],
                        'mime-type' => 'image/webp',
                        'filesize' => $webp_meta_info['filesize'],
                        'source_url' => $webp_meta_info['url'], // Add source URL
                    ];
                  }
                 error_log("[AIG Pro] Added WebP metadata reference for attachment {$attach_id}");
             }
        }

        // Update metadata in database
        wp_update_attachment_metadata($attach_id, $attach_meta);

        // Set Alt Text
        $alt_text = sanitize_text_field($this->current_keyword ?: $title);
        update_post_meta($attach_id, '_wp_attachment_image_alt', $alt_text);

        do_action('aig_image_added_to_media', $attach_id, $post_id);
        error_log("[AIG Pro] Successfully added attachment ID {$attach_id} for post {$post_id}. Optimized: " . ($optimization_performed?'Yes':'No') . ". WebP: " . ($final_webp_path ? 'Yes' : 'No'));

        return wp_get_attachment_url($attach_id);
     }

     /**
      * Helper to generate metadata info for a WebP file.
      */
     private function get_webp_metadata_info(string $webp_path, array $upload_dir_info): ?array {
         if (!file_exists($webp_path)) return null;
         $filesize = filesize($webp_path);
         $filename = basename($webp_path);
         $url = str_replace($upload_dir_info['basedir'], $upload_dir_info['baseurl'], $webp_path);

         return [
             'filename' => $filename,
             'filesize' => $filesize,
             'mime-type' => 'image/webp',
             'path' => str_replace(wp_normalize_path($upload_dir_info['basedir']), '', wp_normalize_path(dirname($webp_path))), // Relative path
             'url' => $url,
         ];
     }

    /** Basic image optimization using GD library. */
    private function optimize_image(string $file_path): bool {
        if (!function_exists('imagecreatefromjpeg') || !file_exists($file_path) || filesize($file_path) === 0) return false;

        $image_info = @getimagesize($file_path); // Use @ to suppress errors on invalid images
        if (!$image_info) return false;
        $mime_type = $image_info['mime'];
        if (!in_array($mime_type, ['image/jpeg', 'image/png', 'image/gif'])) return false; // Only optimize these

        $quality = ($mime_type === 'image/png') ? 9 : 82; // Default: 9 for PNG (lossless max), 82 for JPEG
        $quality = apply_filters('aig_optimization_quality', $quality, $mime_type, $file_path);

        // Adjust JPEG quality based on file size (simple heuristic)
        if ($mime_type === 'image/jpeg') {
            $file_size = filesize($file_path);
            if ($file_size > 1000000) $quality = max(70, min($quality, 75)); // Lower quality for > 1MB
            elseif ($file_size > 300000) $quality = max(75, min($quality, 80)); // Medium quality for > 300KB
            else $quality = max(80, min($quality, 85)); // Higher quality for < 300KB
        }

        $image = null;
        $success = false;
        $memory_limit_needed = max($this->get_memory_limit(), 256 * 1024 * 1024); // Need at least 256M
        $original_memory_limit = @ini_set('memory_limit', $memory_limit_needed . 'M');

        try {
            switch ($mime_type) {
                case 'image/jpeg':
                    $image = @imagecreatefromjpeg($file_path);
                    if ($image) $success = imagejpeg($image, $file_path, (int) $quality);
                    break;
                case 'image/png':
                    $image = @imagecreatefrompng($file_path);
                    if ($image) {
                        // Ensure transparency is preserved
                        if (imageistruecolor($image)) { // Required for alpha blending
                            imagealphablending($image, false); // Required before savesalpha
                            imagesavealpha($image, true);
                        }
                        // PNG quality is compression level (0-9), 9 is max compression (slowest)
                        $png_compression = max(0, min(9, (int)$quality));
                        $success = imagepng($image, $file_path, $png_compression);
                    }
                    break;
                case 'image/gif':
                    // GD GIF optimization is basically non-existent, just resave
                    $image = @imagecreatefromgif($file_path);
                    if ($image) $success = imagegif($image, $file_path);
                    break;
            }
            if ($success) {
                error_log("[AIG Pro Optimize] Successfully re-saved image: {$file_path} (Mime: {$mime_type}, Quality/Level: {$quality})");
            } elseif($image === false) {
                 error_log("[AIG Pro Optimize] Failed to create image resource from file: {$file_path}");
            } else {
                 error_log("[AIG Pro Optimize] Failed to save optimized image: {$file_path}");
            }
        } catch (\Throwable $e) {
            error_log("[AIG Pro Optimize] GD Error: " . $e->getMessage() . " for file: " . $file_path);
            $success = false;
        } finally {
            if ($image) imagedestroy($image);
            if ($original_memory_limit !== false) @ini_set('memory_limit', $original_memory_limit); // Restore original limit
        }
        return $success;
    }

    /** Resizes image using GD if dimensions exceed max_dimension. */
    private function resize_image_if_needed(string $file_path, int $max_dimension): bool {
         if (!function_exists('imagecreatefromjpeg') || !file_exists($file_path) || $max_dimension <= 0) return false;

         $image_info = @getimagesize($file_path);
         if (!$image_info) return false;
         $width = $image_info[0];
         $height = $image_info[1];
         $mime_type = $image_info['mime'];

         if ($width <= $max_dimension && $height <= $max_dimension) {
             return false; // No resize needed
         }

         // Calculate new dimensions
         $ratio = $width / $height;
         if ($width > $height) {
             $new_width = $max_dimension;
             $new_height = (int) round($max_dimension / $ratio);
         } else {
             $new_height = $max_dimension;
             $new_width = (int) round($max_dimension * $ratio);
         }

         $src_image = null;
         $dst_image = null;
         $resized = false;
         $memory_limit_needed = max($this->get_memory_limit(), 256 * 1024 * 1024);
         $original_memory_limit = @ini_set('memory_limit', $memory_limit_needed . 'M');

         try {
             switch ($mime_type) {
                 case 'image/jpeg': $src_image = @imagecreatefromjpeg($file_path); break;
                 case 'image/png': $src_image = @imagecreatefrompng($file_path); break;
                 case 'image/gif': $src_image = @imagecreatefromgif($file_path); break;
                 case 'image/webp': $src_image = @imagecreatefromwebp($file_path); break; // Add WebP source support
                 default: return false;
             }
             if (!$src_image) {
                 error_log("[AIG Pro Resize] Failed to create source image resource from: {$file_path}");
                 return false;
             }

             $dst_image = imagecreatetruecolor($new_width, $new_height);
             if (!$dst_image) return false;

             // Preserve transparency for PNG and GIF and WebP
             if ($mime_type === 'image/png' || $mime_type === 'image/webp') {
                 imagealphablending($dst_image, false);
                 imagesavealpha($dst_image, true);
                 $transparent_alpha = imagecolorallocatealpha($dst_image, 0, 0, 0, 127);
                 imagefill($dst_image, 0, 0, $transparent_alpha);
             } elseif ($mime_type === 'image/gif') {
                 $transparent_index = imagecolortransparent($src_image);
                 if ($transparent_index >= 0) {
                     $t_color = imagecolorsforindex($src_image, $transparent_index);
                     $transparent_new = imagecolorallocate($dst_image, $t_color['red'], $t_color['green'], $t_color['blue']);
                     imagefill($dst_image, 0, 0, $transparent_new);
                     imagecolortransparent($dst_image, $transparent_new);
                 }
             }

             if (!imagecopyresampled($dst_image, $src_image, 0, 0, 0, 0, $new_width, $new_height, $width, $height)) {
                  error_log("[AIG Pro Resize] imagecopyresampled failed for: {$file_path}");
                 return false;
             }

             // Save resized image, overwriting original temporary file
             $save_quality = ($mime_type === 'image/jpeg') ? 90 : 9; // Use high quality for intermediate resize save
             if ($mime_type === 'image/png') $save_quality = 6; // Mid-range compression for PNG resize save

             switch ($mime_type) {
                 case 'image/jpeg': $resized = imagejpeg($dst_image, $file_path, $save_quality); break;
                 case 'image/png': $resized = imagepng($dst_image, $file_path, $save_quality); break;
                 case 'image/gif': $resized = imagegif($dst_image, $file_path); break;
                 case 'image/webp': $resized = imagewebp($dst_image, $file_path, 90); break; // High quality for WebP resize
             }
             if($resized) error_log("[AIG Pro Resize] Resized image to {$new_width}x{$new_height}: {$file_path}");

         } catch (\Throwable $e) {
             error_log("[AIG Pro Resize] GD Error: " . $e->getMessage() . " for file: " . $file_path);
             $resized = false;
         } finally {
             if ($src_image) imagedestroy($src_image);
             if ($dst_image) imagedestroy($dst_image);
             if ($original_memory_limit !== false) @ini_set('memory_limit', $original_memory_limit);
         }

         if ($resized) clearstatcache(true, $file_path); // Update file stats if resized
         return $resized;
    }

    /** Converts image to WebP format using GD. */
    private function convert_to_webp(string $file_path): ?string {
        if (!function_exists('imagewebp') || !file_exists($file_path)) return null;

        $image_info = @getimagesize($file_path);
        if (!$image_info) return null;
        $mime_type = $image_info['mime'];
        if (!in_array($mime_type, ['image/jpeg', 'image/png', 'image/gif'])) {
             error_log("[AIG Pro WebP] Unsupported source mime type for WebP conversion: {$mime_type}");
             return null; // Only convert these common types
        }

        // Output path in the same temp directory
        $output_path = preg_replace('/\.(jpe?g|png|gif)$/i', '.webp', $file_path);
        if ($output_path === $file_path) $output_path .= '.webp'; // Append if extension wasn't replaced

        $image = null;
        $success = false;
        $quality = apply_filters('aig_webp_quality', 80); // Filterable quality
        $memory_limit_needed = max($this->get_memory_limit(), 256 * 1024 * 1024);
        $original_memory_limit = @ini_set('memory_limit', $memory_limit_needed . 'M');

        try {
            switch ($mime_type) {
                case 'image/jpeg': $image = @imagecreatefromjpeg($file_path); break;
                case 'image/png':
                    $image = @imagecreatefrompng($file_path);
                    if ($image && imageistruecolor($image)) {
                        // Preserve alpha transparency for PNG -> WebP
                        imagealphablending($image, false); // Need to disable blending before savealpha
                        imagesavealpha($image, true);
                    }
                    break;
                case 'image/gif': $image = @imagecreatefromgif($file_path); break;
            }
            if (!$image) {
                error_log("[AIG Pro WebP] Failed to create source image resource for WebP conversion from: {$file_path}");
                return null;
            }

            $success = imagewebp($image, $output_path, (int) $quality);
            if($success) error_log("[AIG Pro WebP] Successfully converted to WebP: {$output_path} (Quality: {$quality})");
            else error_log("[AIG Pro WebP] imagewebp() function failed for output: {$output_path}");

        } catch (\Throwable $e) {
            error_log("[AIG Pro WebP] GD Error during conversion: " . $e->getMessage() . " for file: " . $file_path);
            $success = false;
        } finally {
            if ($image) imagedestroy($image);
            if ($original_memory_limit !== false) @ini_set('memory_limit', $original_memory_limit);
        }

        // Return path only if successful and file exists
        if ($success && file_exists($output_path)) {
            return $output_path;
        } elseif (file_exists($output_path)) {
            unlink($output_path); // Clean up failed conversion attempt
        }
        return null;
    }

    // --- Prompt Generation (Refined slightly) ---

    /**
     * Creates a prompt for the featured image.
     *
     * @param string $primary_keyword The main keyword for the image
     * @param string $post_title The post title for context
     * @param string|null $scene_description Optional AI-generated scene description
     * @return string The complete prompt for image generation
     */
    private function create_featured_image_prompt(string $primary_keyword, string $post_title = '', ?string $scene_description = null): string {
        // Base prompt structure - use scene description if available
        if ($scene_description) {
            // If we have a scene description, use it as the core of the prompt
            $prompt = sprintf('%s of %s', $this->image_style, $scene_description);
        } else {
            // Otherwise use the traditional keyword-based approach
            $prompt = sprintf('%s of "%s"', $this->image_style, $primary_keyword);

            // Add context from post title if different and useful
            $cleaned_title = $this->clean_keyword($this->extract_primary_keyword($post_title));
            if (!empty($post_title) && strcasecmp($primary_keyword, $cleaned_title) !== 0 && strlen($post_title) < 100) { // Add title context if not too long
                $prompt .= sprintf(', in the context of "%s"', wp_strip_all_tags($post_title));
            }

            // Add style-specific keywords
            switch ($this->image_style) {
                 case 'cinematic photography': $prompt .= ', dramatic lighting, high detail, cinematic composition, film grain'; break;
                 case 'professional photography': $prompt .= ', sharp focus, clean background, studio lighting or natural light, professional grade'; break;
                 case 'illustration': $prompt .= ', illustrated style, detailed drawing, vibrant colors'; break;
                 case 'digital art': $prompt .= ', digital painting, concept art, high fantasy or sci-fi elements if appropriate'; break;
                 case 'vector art': $prompt .= ', flat design, clean lines, vector illustration, svg style'; break;
                 case 'minimalist design': $prompt .= ', simple shapes, limited color palette, clean background, abstract'; break;
                 case 'stock photo': $prompt .= ', generic stock photo style, bright lighting, slightly generic subject'; break;
                 case 'photorealistic': $prompt .= ', hyperrealistic, 8k resolution, detailed textures, lifelike'; break;
                 default: $prompt .= ', high quality photo'; // Default addition
            }
        }

        // Add composition hints based on setting
        switch ($this->featured_text_style) {
             case 'centered': $prompt .= ', centered subject, balanced composition'; break;
             case 'overlay': $prompt .= ', copy space in lower third, subject focused higher'; break;
             case 'banner': $prompt .= ', copy space in upper third or center, panoramic aspect ratio if possible'; break;
             case 'minimal': $prompt .= ', negative space, minimalist aesthetic'; break;
             case 'rule_of_thirds': $prompt .= ', rule of thirds composition, visually interesting layout'; break;
        }

        // Add aspect ratio if specified in settings
        $aspect_ratio_setting = apply_filters('aig_aspect_ratio', 'auto'); // Add this to settings
        if ($aspect_ratio_setting !== 'auto') {
            $prompt .= ", {$aspect_ratio_setting} aspect ratio";
        }

        // General quality descriptors
        $prompt .= ', visually appealing, high resolution, clear subject matter';

        // Explicitly exclude text and add negative prompts
        $negative_prompt = apply_filters('aig_negative_prompt', 'ugly, deformed, disfigured, poor quality, bad anatomy, worst quality, low quality, lowres, watermark, signature, text, words, letters, captions, titles, blurry, jpeg artifacts, extra fingers, mutated hands, poorly drawn hands, malformed limbs, extra limbs, cloned face, mutilated, out of frame, draft, fog, haze, ugly, tiling, poorly drawn feet, poorly drawn face, out of frame, extra limbs, disfigured, deformed, body out of frame, blurry, bad anatomy, blurred, watermark, grainy, signature, cut off, draft');
        $prompt .= ", negative prompt: ({$negative_prompt})";

        return apply_filters('aig_featured_image_prompt', trim(preg_replace('/\s+/', ' ', $prompt)), $primary_keyword, $post_title, $scene_description);
    }

    /**
     * Creates a prompt for a content image.
     *
     * @param string $context_text The main context text for the image
     * @param string $post_title The post title for context
     * @param string|null $scene_description Optional AI-generated scene description
     * @return string The complete prompt for image generation
     */
    private function create_content_image_prompt(string $context_text, string $post_title = '', ?string $scene_description = null): string {
        // Base prompt structure - use scene description if available
        if ($scene_description) {
            // If we have a scene description, use it as the core of the prompt
            $prompt = sprintf('%s of %s', $this->image_style, $scene_description);
        } else {
            // Otherwise use the traditional keyword-based approach
            $prompt = sprintf('%s depicting or illustrating "%s"', $this->image_style, $context_text);

            // Add style-specific keywords for content
            switch ($this->image_style) {
                 case 'cinematic photography': $prompt .= ', atmospheric scene, detailed environment'; break;
                 case 'professional photography': $prompt .= ', clear subject, good lighting, relevant setting'; break;
                 case 'illustration': $prompt .= ', clear visual representation, informative illustration'; break;
                 case 'digital art': $prompt .= ', artistic interpretation, related visual'; break;
                 case 'vector art': $prompt .= ', simple icon or diagram style, informative graphic'; break;
                 default: $prompt .= ', relevant high quality image';
            }

            // Add context from post title if significantly different and not too long
            $cleaned_title_keyword = $this->extract_primary_keyword($post_title);
            if (!empty($post_title) && strcasecmp($context_text, $cleaned_title_keyword) !== 0 && strlen($post_title) < 100) {
                 $prompt .= sprintf(', related to the overall topic "%s"', wp_strip_all_tags($post_title));
            }
        }

        // Add aspect ratio if specified in settings
        $aspect_ratio_setting = apply_filters('aig_content_aspect_ratio', 'auto'); // Add this to settings
        if ($aspect_ratio_setting !== 'auto') {
            $prompt .= ", {$aspect_ratio_setting} aspect ratio";
        }

        // General quality descriptors and text exclusion
        $prompt .= ', detailed, relevant, high quality';

        // Explicitly exclude text and add negative prompts
        $negative_prompt = apply_filters('aig_content_negative_prompt', 'ugly, deformed, disfigured, poor quality, bad anatomy, worst quality, low quality, lowres, watermark, signature, text, words, letters, captions, titles, blurry, jpeg artifacts, extra fingers, mutated hands, poorly drawn hands, malformed limbs, extra limbs, cloned face, mutilated, out of frame, draft, fog, haze, ugly, tiling, poorly drawn feet, poorly drawn face, out of frame, extra limbs, disfigured, deformed, body out of frame, blurry, bad anatomy, blurred, watermark, grainy, signature, cut off, draft');
        $prompt .= ", negative prompt: ({$negative_prompt})";

        return apply_filters('aig_content_image_prompt', trim(preg_replace('/\s+/', ' ', $prompt)), $context_text, $post_title, $scene_description);
    }

    // --- Cache Clearing ---

    /** AJAX handler for clearing plugin caches. */
    public function ajax_clear_cache(): void {
         check_ajax_referer('aig_clear_cache', 'nonce');
         if (!current_user_can('manage_options')) {
             wp_send_json_error(__('Permission denied.', 'auto-image-generator-pro'), 403);
             return;
         }
         $cleared_count = $this->clear_all_plugin_cache();
         wp_send_json_success([
             'message' => sprintf(__('Cache cleared successfully. %d transient entries removed.', 'auto-image-generator-pro'), $cleared_count)
         ]);
    }

    /**
     * AJAX handler for rebuilding the embedding index.
     */
    public function ajax_rebuild_index(): void {
        // Check nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'aig_nonce')) {
            wp_send_json_error(['message' => 'Invalid security token.']);
            return;
        }

        // Check permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error(['message' => 'You do not have permission to rebuild the index.']);
            return;
        }

        // Start the rebuild process
        $this->start_rebuild_embedding_index();

        wp_send_json_success(['message' => 'Embedding index rebuild started. This may take some time.']);
    }



    /**
     * Process a batch of attachments for embedding generation.
     *
     * @param array $attachment_ids Array of attachment IDs
     */
    public function process_embedding_batch(array $attachment_ids): void {
        if (empty($attachment_ids)) {
            return;
        }

        foreach ($attachment_ids as $attachment_id) {
            $this->generate_and_store_embedding($attachment_id);

            // Add a small delay to avoid API rate limits
            usleep(500000); // 0.5 seconds
        }
    }



    /**
     * Generate and store embedding for a single attachment.
     *
     * @param int $attachment_id Attachment ID
     * @return bool Success or failure
     */
    private function generate_and_store_embedding(int $attachment_id): bool {
        // Skip if not an image
        if (!wp_attachment_is_image($attachment_id)) {
            return false;
        }

        // Get attachment details
        $attachment = get_post($attachment_id);
        if (!$attachment) {
            return false;
        }

        // Prepare text for embedding
        $text_parts = [];

        // Title
        if (!empty($attachment->post_title)) {
            $text_parts[] = $attachment->post_title;
        }

        // Caption
        if (!empty($attachment->post_excerpt)) {
            $text_parts[] = $attachment->post_excerpt;
        }

        // Alt text
        $alt_text = get_post_meta($attachment_id, '_wp_attachment_image_alt', true);
        if (!empty($alt_text)) {
            $text_parts[] = $alt_text;
        }

        // Description
        if (!empty($attachment->post_content)) {
            $text_parts[] = $attachment->post_content;
        }

        // Filename (without extension)
        $filename = pathinfo(get_attached_file($attachment_id), PATHINFO_FILENAME);
        $filename = str_replace(['-', '_'], ' ', $filename);
        if (!empty($filename)) {
            $text_parts[] = $filename;
        }

        // Combine all text
        $text = implode(' ', $text_parts);

        if (empty($text)) {
            // If no text is available, use a generic description based on mime type
            $mime_type = get_post_mime_type($attachment_id);
            $text = "Image of type $mime_type";
        }

        // Generate and store embedding
        $embedding = $this->generate_embedding($text);
        if ($embedding) {
            return $this->store_image_embedding($attachment_id, $embedding);
        }

        return false;
    }

    /** Clears all transients used by this plugin. Returns number of deleted transient groups. */
    public function clear_all_plugin_cache(): int {
        global $wpdb;
        $total_cleared = 0;

        // Clear transients from database
        $prefixes = [
            '_transient_' . AIG_PRO_GEMINI_CACHE_PREFIX,
            '_transient_timeout_' . AIG_PRO_GEMINI_CACHE_PREFIX,
            '_transient_' . AIG_PRO_POST_CACHE_PREFIX,
            '_transient_timeout_' . AIG_PRO_POST_CACHE_PREFIX,
            '_transient_' . AIG_PRO_CACHE_PREFIX, // Legacy general prefix
            '_transient_timeout_' . AIG_PRO_CACHE_PREFIX, // Legacy timeout prefix
        ];
        $like_clauses = [];
        foreach ($prefixes as $prefix) {
            $like_clauses[] = $wpdb->prepare("option_name LIKE %s", $wpdb->esc_like($prefix) . '%');
        }
        $sql = "DELETE FROM {$wpdb->options} WHERE " . implode(' OR ', $like_clauses);
        $deleted_count = $wpdb->query($sql);
        $total_cleared += (int) $deleted_count;

        // Clear related WP Cache groups if used
        wp_cache_flush_group('aig_local'); // For posts_without_images cache
        // Note: Transients don't use WP Cache groups by default unless an object cache backend is active

        error_log("[AIG Pro] Cleared plugin cache. Removed {$deleted_count} transient entries from options table. Total cleared: {$total_cleared}");
        return $total_cleared;
    }

     // --- Background Task Notifications ---

    /** Adds a notification for the user about background task completion. */
    private function add_completion_notification(int $user_id, int $post_id, int $image_count, string $message): void {
        if ($user_id <= 0) return;
        // Use update_user_meta with single = false to store multiple notifications easily
        $notice_data = [
            'message' => $message,
            'time' => time(),
            'post_id' => $post_id,
            'image_count' => $image_count,
            'post_title' => $post_id > 0 ? (get_the_title($post_id) ?: '#' . $post_id) : '',
            'edit_link' => $post_id > 0 ? get_edit_post_link($post_id, 'raw') : null, // Use 'raw' context
        ];
        add_user_meta($user_id, AIG_PRO_NOTIFICATION_META, $notice_data, false);

        // Optional: Prune old notifications if list grows too large
        $notifications = get_user_meta($user_id, AIG_PRO_NOTIFICATION_META, false);
        $max_notifications = apply_filters('aig_max_user_notifications', 10);
        if (count($notifications) > $max_notifications) {
            // Delete the oldest ones
            $to_delete = array_slice($notifications, 0, count($notifications) - $max_notifications);
            foreach($to_delete as $old_notice) {
                // Need the specific value to delete when single=false
                delete_user_meta($user_id, AIG_PRO_NOTIFICATION_META, $old_notice);
            }
        }
    }

    /** Displays stored notifications as admin notices and clears them. */
    public function display_completion_notifications(): void {
        if (!current_user_can('edit_posts') || !is_admin()) return; // Allow editors to see notices for their posts too

        $user_id = get_current_user_id();
        $notifications = get_user_meta($user_id, AIG_PRO_NOTIFICATION_META, false);

        if (!empty($notifications) && is_array($notifications)) {
            foreach ($notifications as $notice) {
                // Ensure notice is in expected format
                if (!is_array($notice) || !isset($notice['message'], $notice['time'], $notice['image_count'])) continue;

                $status_class = ($notice['image_count'] > 0) ? 'notice-success' : 'notice-warning';
                $icon = ($notice['image_count'] > 0) ? '<span class="dashicons dashicons-yes-alt" style="color:green;"></span>' : '<span class="dashicons dashicons-warning" style="color:#ffb900;"></span>';

                ?>
                <div class="notice <?php echo esc_attr($status_class); ?> is-dismissible aig-notice-background">
                    <p>
                        <strong><?php esc_html_e('Auto Image Generator:', 'auto-image-generator-pro'); ?></strong>
                        <?php echo $icon; // phpcs:ignore WordPress.Security.EscapeOutput.OutputNotEscaped -- Icon HTML is safe ?>
                        <?php echo esc_html($notice['message']); ?>

                        <?php if (!empty($notice['edit_link'])): ?>
                            <a href="<?php echo esc_url($notice['edit_link']); ?>" style="margin-left:10px; text-decoration:none;">
                                <span class="dashicons dashicons-edit"></span> <?php esc_html_e('View/Edit Post', 'auto-image-generator-pro'); ?>
                            </a>
                        <?php endif; ?>

                        <small style="margin-left:10px; color:#777;">(<?php printf(esc_html__('%s ago', 'auto-image-generator-pro'), esc_html(human_time_diff($notice['time']))); ?>)</small>
                    </p>
                </div>
                <?php
            }
            // Clear all notifications for this user after displaying them
            delete_user_meta($user_id, AIG_PRO_NOTIFICATION_META);
        }
    }

    // --- Dashboard Widget ---

    public function add_dashboard_widget(): void {
        // Show widget only to users who can manage options (or edit posts, adjust as needed)
        if (current_user_can('manage_options')) {
            wp_add_dashboard_widget(
                'aig_dashboard_widget',
                __('Auto Image Generator Status', 'auto-image-generator-pro'),
                [$this, 'render_dashboard_widget']
            );
        }
    }

    /** Renders the improved dashboard widget content. */
    public function render_dashboard_widget(): void {
        global $wpdb;
        $supported_types_in = "'" . implode("','", array_map('esc_sql', apply_filters('aig_supported_post_types', ['post']))) . "'";
        $total_posts = $wpdb->get_var("SELECT COUNT(*) FROM {$wpdb->posts} WHERE post_type IN ({$supported_types_in}) AND post_status = 'publish'");

        // Get a sample of posts needing images (use cached function)
        $limit_check = 100; // Check a larger number for a more accurate count indication
        $posts_needing_images_sample = $this->get_posts_without_images(5); // Get 5 for display
        $needs_images_count_approx = count($this->get_posts_without_images($limit_check)); // Count up to limit

        echo '<p>' . sprintf(esc_html__('Quick check on %d published posts:', 'auto-image-generator-pro'), $total_posts) . '</p>';

        if ($needs_images_count_approx > 0) {
             echo '<div class="aig-widget-status needs-images">';
             echo '<span class="dashicons dashicons-warning"></span>';
             echo '<div><strong>' . sprintf(esc_html__('%d+ posts may need images (based on %d checked).', 'auto-image-generator-pro'), $needs_images_count_approx, $limit_check) . '</strong>';
             echo '<p style="margin: 5px 0 0; font-size:0.9em;">' . __('Recent examples possibly needing images:', 'auto-image-generator-pro') . '</p>';
             echo '</div></div>';

             if (!empty($posts_needing_images_sample)) {
                 echo '<ul>';
                 foreach ($posts_needing_images_sample as $post) {
                     printf('<li><a href="%s" target="_blank">%s</a></li>',
                         esc_url(get_edit_post_link($post->ID)),
                         esc_html(wp_trim_words($post->post_title, 8, '...'))
                     );
                 }
                 if($needs_images_count_approx > count($posts_needing_images_sample)) echo '<li>' . __('...and potentially more.', 'auto-image-generator-pro') . '</li>';
                 echo '</ul>';
             }

             echo '<a href="' . esc_url(admin_url('admin.php?page=' . AIG_PRO_SETTINGS_SLUG)) . '" class="button button-primary">' . __('Go to Image Generation Page', 'auto-image-generator-pro') . '</a>';

        } else {
            echo '<div class="aig-widget-status all-good">';
            echo '<span class="dashicons dashicons-yes-alt"></span>';
            echo '<strong>' . __('All recent posts seem to have images!', 'auto-image-generator-pro') . '</strong>';
            echo '</div>';
            echo '<a href="' . esc_url(admin_url('admin.php?page=' . AIG_PRO_SETTINGS_SLUG)) . '" class="button button-secondary">' . __('View Settings / Generate Manually', 'auto-image-generator-pro') . '</a>';
        }

        echo '<p class="widget-footer"><small>' . __('Status based on published posts lacking a featured image or `<img>` tag in content. Cache active.', 'auto-image-generator-pro') . '</small></p>';
    }

    // --- REST API Endpoint ---

    public function register_rest_api(): void {
        register_rest_route('aig/v1', '/generate', [
            'methods' => \WP_REST_Server::CREATABLE, // POST requests
            'callback' => [$this, 'rest_generate_images'],
            'permission_callback' => fn() => current_user_can('edit_posts'), // Allow users who can edit posts
            'args' => [
                'post_id' => [
                    'required' => true,
                    'validate_callback' => fn($param) => is_numeric($param) && $param > 0,
                    'sanitize_callback' => 'absint',
                    'description' => __('The ID of the post to generate images for.', 'auto-image-generator-pro'),
                ],
                'count' => [
                    'required' => false,
                    'default' => 1,
                    'validate_callback' => fn($param) => is_numeric($param) && $param > 0 && $param <= AIG_PRO_MAX_IMAGES_LIMIT,
                    'sanitize_callback' => fn($param) => min($this->max_images, max(1, absint($param))),
                     'description' => sprintf(__('Number of images to generate (1-%d).', 'auto-image-generator-pro'), $this->max_images),
                ],
                'featured_only' => [
                    'required' => false,
                    'default' => false,
                    'validate_callback' => 'is_boolean',
                    'sanitize_callback' => 'rest_sanitize_boolean',
                    'description' => __('Whether to only generate and set the featured image.', 'auto-image-generator-pro'),
                ],
            ],
            'schema' => [ // Basic schema definition
                '$schema' => 'http://json-schema.org/draft-04/schema#',
                'title' => 'Generate Images',
                'description' => 'Generates AI images for a specific post.',
                'type' => 'object',
                 'properties' => [
                    'success' => ['type' => 'boolean'],
                    'message' => ['type' => 'string'],
                    'count' => ['type' => 'integer'],
                    'generated_images' => ['type' => 'array', 'items' => ['type' => 'object']],
                 ]
            ],
        ]);
    }



    /**
     * Process a single attachment for embedding
     */
    public function process_embedding_single(array $args): void {
        $attachment_id = $args['attachment_id'] ?? 0;

        if (empty($attachment_id)) {
            error_log("[AIG Pro] No attachment ID provided for single embedding processing");
            return;
        }

        $attachment = get_post($attachment_id);
        if (!$attachment || $attachment->post_type !== 'attachment' || strpos($attachment->post_mime_type, 'image/') !== 0) {
            error_log("[AIG Pro] Invalid attachment ID or not an image: $attachment_id");
            return;
        }

        // Collect text from various sources
        $text_parts = [];

        // Title
        if (!empty($attachment->post_title)) {
            $text_parts[] = $attachment->post_title;
        }

        // Caption
        if (!empty($attachment->post_excerpt)) {
            $text_parts[] = $attachment->post_excerpt;
        }

        // Alt text
        $alt_text = get_post_meta($attachment_id, '_wp_attachment_image_alt', true);
        if (!empty($alt_text)) {
            $text_parts[] = $alt_text;
        }

        // Description
        if (!empty($attachment->post_content)) {
            $text_parts[] = $attachment->post_content;
        }

        // Filename (without extension)
        $filename = pathinfo(get_attached_file($attachment_id), PATHINFO_FILENAME);
        $filename = str_replace(['-', '_'], ' ', $filename);
        if (!empty($filename)) {
            $text_parts[] = $filename;
        }

        // Combine all text
        $text = implode(' ', $text_parts);

        if (empty($text)) {
            // If no text is available, use a generic description based on mime type
            $mime_type = get_post_mime_type($attachment_id);
            $text = "Image of type $mime_type";
        }

        // Generate and store embedding
        $embedding = $this->generate_embedding($text);
        if ($embedding) {
            $this->store_image_embedding($attachment_id, $embedding);
            error_log("[AIG Pro] Successfully processed embedding for attachment ID: $attachment_id");
        } else {
            error_log("[AIG Pro] Failed to generate embedding for attachment ID: $attachment_id");
        }
    }







    /**
     * Analyze image content using AI vision capabilities.
     * This is a more advanced feature that uses AI to "see" what's in the image.
     *
     * @param string $file_path Path to the image file
     * @return string Description of the image content or empty string on failure
     */
    private function analyze_image_content(string $file_path): string {
        if (empty($this->api_key) || !file_exists($file_path)) {
            return '';
        }

        // Check if we should use the cache
        $cache_key = 'aig_image_analysis_' . md5($file_path . filemtime($file_path));
        $cached_analysis = get_transient($cache_key);

        if ($cached_analysis !== false && !$this->force_alt_text_regeneration) {
            return $cached_analysis;
        }

        try {
            // Convert image to base64
            $image_data = base64_encode(file_get_contents($file_path));
            if (empty($image_data)) {
                return '';
            }

            // Prepare the API request for image analysis
            // This is a simplified example - actual implementation would depend on the specific AI provider
            $response = $this->call_vision_api($image_data);

            if (!empty($response)) {
                // Cache the result for future use (1 week)
                set_transient($cache_key, $response, 7 * DAY_IN_SECONDS);
                return $response;
            }
        } catch (\Exception $e) {
            error_log("[AIG Pro] Image analysis failed: " . $e->getMessage());
        }

        return '';
    }

    /**
     * Call the vision API to analyze an image.
     * This is a placeholder method that would be implemented based on the specific AI provider.
     *
     * @param string $image_data Base64-encoded image data
     * @return string Description of the image content
     */
    private function call_vision_api(string $image_data): string {
        // This is a placeholder implementation
        // In a real implementation, this would call a vision API like Google Cloud Vision, Azure Computer Vision, etc.

        // For now, we'll use our text generation API with a special prompt
        $prompt = "Analyze this image and describe what you see in detail. Focus on the main subjects, actions, setting, colors, and mood.";

        // Here we would include the image data in the API call
        // Since most text APIs don't support image input directly, this would typically use a multimodal API

        // For this example, we'll return a placeholder message
        return "Image analysis not implemented in this version.";

        // In a real implementation with a vision API, we would:
        // 1. Call the API with the image data
        // 2. Process the response to extract relevant information
        // 3. Return a concise description of the image content
    }

    /**
     * Start the process of rebuilding the embedding index
     */
    private function start_rebuild_embedding_index(): void {
        // Get all image attachments
        $args = [
            'post_type' => 'attachment',
            'post_mime_type' => 'image',
            'post_status' => 'inherit',
            'posts_per_page' => -1,
            'fields' => 'ids',
        ];

        $attachment_ids = get_posts($args);

        if (empty($attachment_ids)) {
            return;
        }

        // Randomize the order to distribute the load
        shuffle($attachment_ids);

        // Start the batch process
        if (function_exists('as_schedule_single_action') && class_exists('ActionScheduler')) {
            // Use Action Scheduler if available
            as_schedule_single_action(time(), 'aig_rebuild_embedding_batch', [[
                'attachment_ids' => $attachment_ids,
                'batch_size' => 20,
                'offset' => 0
            ]]);
        } else {
            // Fallback to WP Cron
            wp_schedule_single_event(time(), 'aig_rebuild_embedding_batch', [[
                'attachment_ids' => $attachment_ids,
                'batch_size' => 20,
                'offset' => 0
            ]]);
        }

        error_log("[AIG Pro] Started embedding index rebuild for " . count($attachment_ids) . " images");
    }







    /** Callback for the REST API endpoint. */
    public function rest_generate_images(\WP_REST_Request $request): \WP_REST_Response|\WP_Error {
         $post_id = $request['post_id'];
         $count = $request['count'];
         $featured_only = $request['featured_only'];

         // Reload options in REST context? Usually not needed if singleton is initialized early.
         // $this->load_options();

         if (empty($this->api_key)) {
             return new \WP_Error('aig_no_api_key', __('API key is not configured in plugin settings.', 'auto-image-generator-pro'), ['status' => 400]);
         }

         $post = get_post($post_id);
         $supported_types = apply_filters('aig_supported_post_types', ['post']);
         if (!$post || !in_array($post->post_type, $supported_types, true)) {
             return new \WP_Error('aig_invalid_post', __('Invalid post ID or unsupported post type.', 'auto-image-generator-pro'), ['status' => 404]);
         }

         // Check if we should use background processing
         if ($this->background_tasks_enabled && $count > $this->background_task_threshold) {
             $user_id = get_current_user_id();

             // Use our unified scheduling method
             $scheduled = $this->schedule_post_processing($post_id, $count, $featured_only, $user_id);

             if ($scheduled) {
                 return new \WP_REST_Response([
                     'success' => true,
                     'message' => __('Images scheduled for generation in the background.', 'auto-image-generator-pro'),
                     'background' => true
                 ], 202); // HTTP 202 Accepted
             } else {
                 // Check if already scheduled
                 if (function_exists('as_next_scheduled_action')) {
                     $existing_action = as_next_scheduled_action(
                         'aig_process_post_action_scheduler',
                         [
                             'post_id' => $post_id,
                             'count' => $count,
                             'featured_only' => $featured_only,
                             'user_id' => $user_id
                         ],
                         'aig-image-generation'
                     );

                     if ($existing_action) {
                         return new \WP_REST_Response([
                             'success' => true,
                             'message' => __('This post is already scheduled for image generation.', 'auto-image-generator-pro'),
                             'background' => true
                         ], 202); // HTTP 202 Accepted
                     }
                 }

                 if (wp_next_scheduled('aig_background_generate_images', [$post_id, $count, $featured_only, $user_id])) {
                     return new \WP_REST_Response([
                         'success' => true,
                         'message' => __('This post is already scheduled for image generation.', 'auto-image-generator-pro'),
                         'background' => true
                     ], 202); // HTTP 202 Accepted
                 }

                 return new \WP_Error('scheduling_failed', __('Failed to schedule background task.', 'auto-image-generator-pro'), ['status' => 500]);
             }
         }

         // Process immediately
         try {
             $result = $this->generate_images_for_post($post, $count, $featured_only);
             $generated_count = count($result);

             if ($generated_count === 0) {
                 // Consider returning a success response with count 0, or an error?
                 // Returning an error might be clearer that the primary action failed.
                 return new \WP_Error('aig_generation_failed', __('No images were generated. Check API key, prompts, limits, and logs.', 'auto-image-generator-pro'), ['status' => 500]);
             }

             // Success response
             return new \WP_REST_Response([
                 'success' => true,
                 'message' => sprintf(_n('%d image generated and added.', '%d images generated and added.', $generated_count, 'auto-image-generator-pro'), $generated_count),
                 'count' => $generated_count,
                 'generated_images' => $result, // Include details of generated images
             ], 200); // HTTP 200 OK

         } catch (\Throwable $t) {
             error_log('[AIG Pro REST Error] ' . $t->getMessage());
             // Return a generic server error
             return new \WP_Error('aig_generation_error', __('An error occurred during image generation: ', 'auto-image-generator-pro') . $t->getMessage(), ['status' => 500]);
         }
    }

     // --- Post Edit Screen Meta Box ---

    public function add_meta_box(): void {
        $supported_types = apply_filters('aig_supported_post_types', ['post']);
        add_meta_box(
            'aig-generate-metabox',
            __('Auto Image Generator', 'auto-image-generator-pro'),
            [$this, 'render_meta_box'],
            $supported_types,
            'side', // Context
            'default' // Priority
        );
    }

    /** Renders the meta box content on the post edit screen. */
    public function render_meta_box(\WP_Post $post): void {
        if (empty($this->api_key)) {
            echo '<p style="color:#d63638; padding:12px;">⚠️ ' . esc_html_x('API Key not set in Auto Images settings.', 'js translation', 'auto-image-generator-pro') . '</p>';
            return;
        }

        // Nonce specifically for meta box actions (though JS uses the main AJAX nonce)
        // wp_nonce_field('aig_meta_box_action', 'aig_meta_box_nonce');

        $heading_count = substr_count(strtolower($post->post_content), '<h2');
        $has_thumbnail = has_post_thumbnail($post->ID);
        ?>
        <div id="aig-meta-box-content">
            <p><?php esc_html_e('Generate AI images directly for this post:', 'auto-image-generator-pro'); ?></p>

            <input type="hidden" id="aig_post_id_metabox" value="<?php echo esc_attr($post->ID); ?>">

            <div>
                 <label for="aig-count-metabox" style="display:flex; align-items: center; justify-content: space-between;">
                    <?php esc_html_e('Number of images:', 'auto-image-generator-pro'); ?>
                    <select id="aig-count-metabox">
                        <?php for ($i = 1; $i <= $this->max_images; $i++): ?>
                            <option value="<?php echo $i; ?>" <?php selected($i, 1); ?>><?php echo $i; ?></option>
                        <?php endfor; ?>
                    </select>
                 </label>
                 <?php if ($heading_count > 0): ?>
                    <p class="description" style="margin-top:-5px; margin-bottom: 10px;"><?php printf(esc_html_x('%d headings found.', 'js translation', 'auto-image-generator-pro'), $heading_count); ?></p>
                 <?php endif; ?>
            </div>

            <div>
                <label>
                    <input type="checkbox" id="aig-featured-only-metabox" <?php checked(!$has_thumbnail); ?>>
                    <?php esc_html_e('Featured image only', 'auto-image-generator-pro'); ?>
                    <?php if ($has_thumbnail) echo '<span style="color:#777; font-style:italic; margin-left:5px;">(' . esc_html_x('exists', 'js translation', 'auto-image-generator-pro') . ')</span>'; ?>
                </label>
            </div>

            <button type="button" id="aig-generate-btn-metabox" class="button button-primary">
                <span class="dashicons dashicons-camera" style="vertical-align: text-bottom; margin-right: 3px;"></span>
                <?php echo esc_html_x('Generate Images', 'js translation', 'auto-image-generator-pro'); ?>
            </button>

            <div id="aig-status-metabox" style="display: none;"></div> <?php // Status div, initially hidden ?>
        </div>
        <script>
            // Small inline script to show status div when needed by the main embedded JS
            (function() {
                const statusDiv = document.getElementById('aig-status-metabox');
                const btn = document.getElementById('aig-generate-btn-metabox');
                if(btn && statusDiv) {
                    btn.addEventListener('click', () => {
                        statusDiv.style.display = 'flex'; // Show it when button is clicked
                        statusDiv.className = 'aig-status-metabox'; // Reset classes
                    });
                }
            })();
        </script>
        <?php
    }

    // --- Plugin Meta & Helpers ---

    /** Add Settings link to plugin actions */
    public function add_action_links(array $links): array {
        $settings_link = sprintf('<a href="%s">%s</a>',
            esc_url(admin_url('admin.php?page=' . AIG_PRO_SETTINGS_SLUG)),
            __('Settings', 'auto-image-generator-pro')
        );
        array_unshift($links, $settings_link);
        return $links;
    }

    /** Load plugin textdomain */
    public function load_textdomain(): void {
        load_plugin_textdomain('auto-image-generator-pro', false, dirname(plugin_basename(__FILE__)) . '/languages');
    }


} // End AIG_Pro Class

// --- Initialize ---
function aig_pro_init(): void {
    // Ensure initialization happens only once
    if (!function_exists('get_plugin_data')) {
        require_once(ABSPATH . 'wp-admin/includes/plugin.php');
    }
    // Basic check for compatibility before initializing
    if (version_compare(PHP_VERSION, '7.4', '<') || version_compare(get_bloginfo('version'), '5.8', '<')) {
         add_action('admin_notices', function() {
             echo '<div class="notice notice-error"><p>';
             echo '<strong>Auto Image Generator Pro Error:</strong> ';
             echo esc_html__('This plugin requires PHP 7.4+ and WordPress 5.8+. Please update your environment.', 'auto-image-generator-pro');
             echo '</p></div>';
         });
        return;
    }
    AIG_Pro::get_instance();
}
add_action('plugins_loaded', 'aig_pro_init', 5); // Initialize early on plugins_loaded